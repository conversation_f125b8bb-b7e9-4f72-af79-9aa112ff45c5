<template>
	<form @submit="handleSubmit">
		<input name="companyID" :value="companyID" hidden>
		<view class="cropping">
			<view class="style">
				<view class="title">款式</view>
				<view class="style-list">
					<view class="item">
						<input name="styleID" :value="styleIndex !== null ? style[styleIndex].ID : ''" hidden>
						<view class="input">
							<picker :range="style" range-key="name" :value="styleIndex" @change="handleStyle">
								<view class="value" data-tips="请选择款式">
									{{styleIndex !== null ? style[styleIndex].name : ''}}</view>
							</picker>
						</view>
						<text class="btn status success colorLink" @click="chooseColor">快速录入</text>
					</view>
					<view class="item">
						<input class="input" name="color" type="text" v-model="colorName" placeholder="请输入颜色">
					</view>
					<view class="item">
						<input class="input" name="length" type="number" placeholder="请输入长度(非必填)">
					</view>
					<view class="item">
						<input class="input" name="remainder" type="number" placeholder="请输入余数(非必填)">
					</view>
				</view>
			</view>

			<view class="size">
				<view class="title">
					<text class="modelBtnList">
						<button class="btn btnModel" type="primary" @click="handleModelChange('比例')">比例</button>
						<button class="btn btnModel" type="primary" @click="handleModelChange('相同')">相同</button>
						<button class="btn btnModel" type="warn" @click="handleModelChange('取消')">取消</button>
					</text>
					<view class="btnGroup ModelTypeCss">
						<uni-data-checkbox-input mode="tag" :multiple="false" v-model="radioModelTypeList"
							:localdata="ModelType" @change="modelTypeChange"></uni-data-checkbox-input>
					</view>
				</view>
				<view class="title">
					<text>尺寸<text style="color: coral;">{{ModelTypeStr}}</text></text>
					<view class="btnGroup">
						<text class="btn status success addLink" @click="chooseStandard">快速录入</text>
						<button class="btn status success croppingLink" @click="croppingEvent" v-if="styleIndex !== null">此款裁剪单</button>
					</view>
				</view>
				<view class="sizeContent">
					<!-- <view class="size-list" v-for="(item, index) in sizeList" :key="index"> -->
					<view :class="[HasModelType?'size-list':'size-list1']" v-for="(item, index) in sizeList" :key="index">
						<checkbox class="checkbox" value="cb" :checked="item.flag" @click="cbxEvent(item)"  v-if="HasModelType" />
						<input class="input modelNum" name="size" v-model="item.modelv" type="text" v-if="HasModelType">
						<input class="input" name="size" v-model="item.size" type="text" placeholder="请输入尺寸"
							@blur="onchangeEvent">
						<input class="input" v-model="item.quantity" @blur="onchangeAmountEvent(item)" type="number" placeholder="请输入数量">
						<button class="btn" type="warn" size="mini" @click="handleDElSize(index)">-</button>
						<!-- <button class="btn" type="warn" size="mini" @click="handleDElSize(index)" :disabled="sizeList.length <= 1">-</button> -->
					</view>
				</view>
				<button class="btn add" type="primary" size="mini" @click="handleAddSize">+</button>
			</view>
		</view>
		<popup :show="isShowColor" @close="handleToggleColor" style="z-index: 9999;">
			<view class="detailContent">
				<view class="title choseColor">
					<text class="txt">选择颜色</text>
					<view class="btnGroup">
						<text class="btn status success addLink" @click="inputColor">手动录入</text>
					</view>
				</view>
				<view class="cont">
					<view class="jobs">
						<view class="row">
							<uni-data-checkbox mode="tag" :multiple="false" v-model="radioColorList"
								:localdata="JBGYBZ" @change="corlorChange"></uni-data-checkbox>
						</view>
					</view>
					<empty v-if="!sizeStantardList?.length"></empty>
				</view>
			</view>
		</popup>
		<popup :show="isShow" @close="handleToggle" style="z-index: 9999;">
			<view class="detailContent">
				<view class="title">
					<text class="txt">选择尺码标准</text>
					<view class="btnGroup">
						<text class="btn status success addLink org">双击行可选中标准</text>
					</view>
				</view>
				<view class="cont">
					<view class="jobs">
						<view class="row">
							<!-- <view class="cell" style="width: 50rpx;">序号</view> -->
							<view class="cell">标准</view>
							<view class="cell" style="text-align: center;">尺码</view>
							<!-- <view class="cell"></view> -->
						</view>
						<view class="row" v-for="item in sizeStantardList" :key="item.id">
							<!-- <view class="cell" @dblclick="handleToggle(item)" style="width: 50rpx;">{{item.id}}</view> -->
							<view class="cell" @dblclick="handleToggle(item)">{{item.name}}</view>
							<view class="cell" @dblclick="handleToggle(item)">{{item.standard}}</view>
							<!-- <view class="cell">
								<text class="primary"  @click="handleSwapReceive(item)">选中</text>
							</view> -->
						</view>
					</view>
					<empty v-if="!sizeStantardList?.length"></empty>
				</view>
			</view>
		</popup>
		<popup :show="isShowInputColor" @close="handleToggleInputColor" style="z-index: 99999;">
			<view class="detail1">
				<view class="title">
					自定义颜色
				</view>
				<view class="cont">
					<view class="jobs">
						<view class="row">
							<!-- <view class="cell">颜色：</view> -->
							<view class="cell">
								<view class="item">
									<view class="child">
										<input class="input teamInput" v-model="colorInput" type="text" placeholder="请输入颜色">
									</view>
								</view>
							</view>
						</view>
						
						<view class="row justify-content-center">
							<button class="btn submit2 btnsave" type="default" @click="cancelSaveColor">取消</button>
							<button class="btn submit1 btnsave" type="primary" @click="saveColor">保存</button>
						</view>
					</view>
				</view>
			</view>
		</popup>
		<button class="btn submit" form-type="submit" style="z-index: 9998;">新增</button>
	</form>
	<issue></issue>
</template>

<script setup>
	import {
		ref,
		computed,
		toRaw
	} from 'vue'
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		isEmpty
	} from '@/utils/index.js'
	import Https from '@/apis/index.js'
	import userInfoStore from '@/store/userInfo.js'
	import {
		storeToRefs
	} from 'pinia'
	const isShow = ref(false)
	const isShowColor = ref(false)
	const sizeStantardList = ref([])
	const store = userInfoStore()
	let colorName = ref("")
	let radioColorList = ref([])
	let JBGYBZ = ref([]);
	let radioModelTypeList = ref([])
	let ModelType = ref([])
	let ModelTypeStr=ref("")
	let HasModelType=ref(false)
	let isShowInputColor=ref(false)
	let modelTypeNum=ref(0)
	let colorInput=ref('')
	// {
	// 	text: '黄色',
	// 	value: '黄色'
	// }
	const {
		companyID,
		roleID,
		teamID,
		user
	} = storeToRefs(store)
	const handleToggleColor = async (item) => {
		isShowColor.value = !isShowColor.value
		let a = toRaw(radioColorList.value); ///.join(',');

		colorName.value = ""
		radioColorList.value = []
		JBGYBZ.value = []
	}
	const corlorChange=(data)=>{
		// console.log(data)
		// console.log(data.detail.value)
		colorName.value=data.detail.value;
		isShowColor.value = false;
	}
	const modelTypeChange=(data)=>{
		// console.log(data)
		console.log(data.detail.value)
		modelTypeNum.value=data.detail.value;
		sizeList.value.forEach(function(item, index) {
			if(item.flag){
				item.modelv=modelTypeNum.value;
			}
		})
		
		sizeList.value.forEach(function(item, index) {
			item.flag=false;
		})
	}
	const inputColor=()=>{
		colorInput.value=""
		isShowInputColor.value=true;
		// isShowColor.value=false;
	}
	const handleToggleInputColor=()=>{
		isShowInputColor.value=!isShowInputColor.value;
		// isShowColor.value=true;
	}
	const cancelSaveColor=()=>{
		isShowInputColor.value=false;
		// isShowColor.value=true;
	}
	const saveColor=async()=>{
		console.log(inputColor.value)	
		if (isEmpty(colorInput.value)) {
			uni.showToast({
				title: '请输入颜色',
				icon: 'none'
			})
			return
		}
		let formData = {
			userId: user.value.ID.toString(),
			color:colorInput.value
		}
		let r = await Https.Common.AddColors(formData)
		if (!r.Success) {
			uni.showToast({
				icon: 'none',
				mask: true,
				title: "添加失败(" + r.ErrMsg + ")",
				duration: 2000
			});
		}
		else{
			await getColor();
			isShowInputColor.value=false;
		}
	}
	const handleToggle = async (item) => {
		// console.log(item)
		if (item != undefined) {
			let arr = item.standard.split('-')
			if (arr.length > 0) {
				sizeList.value.splice(0)
				arr.forEach(function(item, index) {
					sizeList.value.push({
						flag:false,
						modelv:1,
						size: item,
						quantity: ''
					})
				})
			}

			let standardR = await Https.Process.AddStandard({
				name: user.value.username,
				userId: user.value.ID.toString(),
				standard: item.standard,
				type: 'user',
				sort: '0'
			})
		}
		console.log(sizeList)
		// await getStyle() 
		isShow.value = !isShow.value
	}
	const chooseColor = async () => {
		await getColor();
		console.log(JBGYBZ.value)
		isShowColor.value = true;
	}
	const chooseStandard = () => {
		isShow.value = true;
	}
	const cbxEvent=(item)=>{
		console.log(item)
		item.flag=!item.flag;
		// if(modelTypeNum.value!=0&&item.flag){
		// 	item.modelv=modelTypeNum.value;
		// }
		console.log(item)
	}
	const onchangeEvent = async () => {
		console.log(1111)
		console.log(sizeList.value)
		if (sizeList.value.length > 0) {
			let standrdStr = sizeList.value[0].size;
			sizeList.value.forEach(function(item, index) {
				if (index > 0 && !isEmpty(item.size)) {
					standrdStr += '-' + item.size;
				}
			})
			// console.log(standrdStr)
			// console.log(user.value)
			let standardR = await Https.Process.AddStandard({
				name: user.value.username,
				userId: user.value.ID.toString(),
				standard: standrdStr,
				type: 'user',
				sort: '0'
			})
		} else {
			let standardR = await Https.Process.AddStandard({
				name: user.value.username,
				userId: user.value.ID.toString(),
				standard: '',
				type: 'user',
				sort: '0'
			})
		}
	}
	const onchangeAmountEvent = async (curItem) => {
		sizeList.value.forEach(function(item, index) {
			if (!isEmpty(curItem.quantity)&&HasModelType.value) {
				if(ModelTypeStr.value.indexOf("比例")>-1){
					if(item.modelv==curItem.modelv){
						item.quantity=curItem.quantity;
					}
					else{
						item.quantity=((curItem.quantity/curItem.modelv)*item.modelv).toFixed(0);
					}
				}
				if(ModelTypeStr.value.indexOf("相同")>-1){
					if(item.modelv==curItem.modelv){
						item.quantity=curItem.quantity;
					}
				}
			}
		})
	}
	const handleSubmit = async ({
		detail
	}) => {
		const formData = {
			...detail.value,
			sizeList: sizeList.value.map(item => ({
				flag:false,
				modelv:1,
				size: item.size,
				quantity: Number(item.quantity)
			}))
		}
		// console.log(formData)
		// return;
		if (isEmpty(formData.styleID)) {
			uni.showToast({
				title: '请选择款式',
				icon: 'none'
			})
			return
		}
		if (isEmpty(formData.color)) {
			uni.showToast({
				title: '请输入颜色',
				icon: 'none'
			})
			return
		}
		if (isEmpty(formData.length)) {
			formData.length=0
			// uni.showToast({
			// 	title: '请输入长度',
			// 	icon: 'none'
			// })
			// return
		}
		formData.companyID = Number(formData.companyID)
		formData.styleID = Number(formData.styleID)
		formData.length = Number(formData.length)
		formData.remainder = Number(formData.remainder)
		formData.userID = user.value.ID
		console.log(formData)
		let r = await Https.CroppingRecord.AddNew(formData)
		console.log(r)
		// const { msg: content } = await Https.CroppingRecord.Add(formData)

		// 兼容不同API的响应格式
		let isSuccess = false;
		let errorMsg = "";

		if (r.Success !== undefined) {
			// C# API格式
			isSuccess = r.Success;
			errorMsg = r.ErrMsg || "未知错误";
		} else if (r.code !== undefined) {
			// Go API格式
			isSuccess = r.code === 0;
			errorMsg = r.msg || "未知错误";
		}

		if (!isSuccess) {
			uni.showToast({
				icon: 'none',
				mask: true,
				title: "添加失败(" + errorMsg + ")",
				duration: 2000
			});
		} else {
			uni.showModal({
				title: "添加成功",
				cancelText: '返回上一页',
				confirmText: '继续添加',
				success({
					cancel,
					confirm
				}) {
					if (cancel) {
						uni.navigateBack()
					}
					if (confirm) {
						sizeList.value.forEach(function(item, index) {
							item.quantity = ''
						})
						detail.color = ''
						detail.length = ''
						detail.remainder = ''
						// sizeList.value = [
						// 				{ size: '', quantity: '' },
						// 				{ size: '', quantity: '' },
						// 				{ size: '', quantity: '' },
						// 				{ size: '', quantity: '' },
						// 				{ size: '', quantity: '' }]
						// }
					}
				}
			})
		}
	}

	const sizeList = ref([
		// { size: '', quantity: '' }
	])
	const handleAddSize = () => {
		sizeList.value.push({
			flag:false,
			modelv:1,
			size: '',
			quantity: ''
		})
	}
	const handleDElSize = (index) => {
		sizeList.value.splice(index, 1)
		onchangeEvent()
	}
	const handleModelChange = (modelType) => {
	// console.log(modelType)
		if(modelType=="取消"){
			ModelTypeStr.value="";
			HasModelType.value=false;
			radioModelTypeList.value=[];
		}
		else{
			ModelTypeStr.value="（按"+modelType+"）";
			HasModelType.value=true;
		}
		
		sizeList.value.forEach(function(item, index) {
			item.modelv=1;
			item.quantity='';
			item.flag=false;
		})
	}
	const style = ref([])
	const styleCache = ref([])
	const styleIndex = ref(null)
	const getStyle = async (styleName) => {
		const {
			data
		} = await Https.Style.List({
			companyID: companyID.value
		})
		style.value = data.list.reverse()

		// console.log(styleIndex.value)
		// console.log(styleName)
		style.value.forEach(function(item, index) {
			if (item.name == styleName) {
				// console.log(index+'|'+item.name)
				styleIndex.value = index;
			}
		})
		console.log(styleIndex.value)

		let replyR = await Https.Process.GetStandard()
		// console.log(replyR)
		if (replyR.Success) {
			sizeStantardList.value = replyR.ResData;
		}

		let standardR = await Https.Process.GetMyStandard({
			userId: user.value.ID.toString(),
			userName: ''
		})
		// console.log(standardR)
		if (standardR.Success) {
			styleCache.value = standardR.ResData;
			console.log(styleCache.value)
			if (styleCache.value.length > 0) {
				let arr = styleCache.value[0].standard.split('-')
				if (arr.length > 0) {
					sizeList.value.splice(0)
					arr.forEach(function(item, index) {
						sizeList.value.push({
							flag:false,
							modelv:1,
							size: item,
							quantity: ''
						})
					})
				}
			}
		}
		//await getColor();
	}

	const getColor = async () => {
		JBGYBZ.value.splice(0)
		let colorR = await Https.Common.GetColors({userId:user.value.ID.toString()})
		// console.log(colorR)
		if (colorR.Success) {
			colorR.ResData.data.forEach(function(item, index) {
				// console.log(item)
				// radioColorList.value.push({ text: item.color, value:  item.color })
				JBGYBZ.value.push({
					text: item.color,
					value: item.color
				})
			})
		}
	}
	const handleStyle = async ({
		detail
	}) => {
		styleIndex.value = Number(detail.value)

		let standardR = await Https.Process.GetMyStandard({
			userId: user.value.ID.toString(),
			userName: ''
		})
		console.log(standardR)
		if (standardR.Success) {
			styleCache.value = standardR.ResData;
			console.log(styleCache.value)
			if (styleCache.value.length > 0) {
				let arr = styleCache.value[0].standard.split('-')
				if (arr.length > 0) {
					// 定义尺码排序规则
					const sizeOrder = ['XS', 'S', 'M', 'L', 'XL', 'XXL', '2XL', '3XL', '4XL', '5XL']

					// 对尺码进行排序
					arr.sort((a, b) => {
						const aIndex = sizeOrder.indexOf(a.toUpperCase())
						const bIndex = sizeOrder.indexOf(b.toUpperCase())

						// 如果两个尺码都在标准排序中，按标准顺序排序
						if (aIndex !== -1 && bIndex !== -1) {
							return aIndex - bIndex
						}
						// 如果只有a在标准排序中，a排在前面
						else if (aIndex !== -1 && bIndex === -1) {
							return -1
						}
						// 如果只有b在标准排序中，b排在前面
						else if (aIndex === -1 && bIndex !== -1) {
							return 1
						}
						// 如果都不在标准排序中，按字母顺序排序
						else {
							return a.localeCompare(b)
						}
					})

					console.log('添加页面 - 排序后的尺码:', arr)

					sizeList.value.splice(0)
					arr.forEach(function(item, index) {
						sizeList.value.push({
							flag:false,
							modelv:1,
							size: item,
							quantity: ''
						})
					})
				}
			}
		} else {
			sizeList.value.splice(0)
			sizeList.value.push({
				flag:false,
				modelv:1,
				size: '',
				quantity: ''
			})
			sizeList.value.push({
				flag:false,
				modelv:1,
				size: '',
				quantity: ''
			})
			sizeList.value.push({
				flag:false,
				modelv:1,
				size: '',
				quantity: ''
			})
			sizeList.value.push({
				flag:false,
				modelv:1,
				size: '',
				quantity: ''
			})
			sizeList.value.push({
				flag:false,
				modelv:1,
				size: '',
				quantity: ''
			})
		}
	}
	const croppingEvent = () => {
    if (styleIndex.value !== null && style.value[styleIndex.value]) {
        const currentStyle = style.value[styleIndex.value]
        uni.navigateTo({
            url: '/pagesSub/pages/cropping/cropping?id=' + currentStyle.ID + "&name=" + currentStyle.name
        })
    }
    }
	onLoad(({
		style
	}) => {
		style && getStyle(style)
		
		ModelType.value.splice(0);
		ModelType.value.push({ text: 1, value:  1 })
		ModelType.value.push({ text: 2, value:  2 })
		ModelType.value.push({ text: 3, value:  3 })
		ModelType.value.push({ text: 4, value:  4 })
		ModelType.value.push({ text: 5, value:  5 })
		ModelType.value.push({ text: 6, value:  6 })
		ModelType.value.push({ text: 7, value:  7 })
		ModelType.value.push({ text: 8, value:  8 })
	})
	
	// onLoad(getStyle)
</script>

<style lang="scss" scoped>
	.cropping {
		padding: 20rpx;

		.title {
			font-weight: bold;

			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;
			padding: 0 0rpx;
			border-bottom: 1rpx solid $uni-border-color;
			font-weight: bold;

			.status {
				font-size: 26rpx;
				font-weight: normal;

				&.success {
					color: $uni-color-primary;
				}

				&.fail {
					color: $uni-color-error;
				}
			}

			.btn {
				width: 120rpx;
				padding: 0;
			}
		}

		.btnGroup {
			display: flex;
			align-items: center;

			.addLink {
				width: 110rpx;
				margin: 8rpx;
				padding: 1rpx !important;
			}

			.addLinkCJD {
				width: 140rpx;
				margin: 8rpx;
				padding: 1rpx !important;
			}
		}

		.colorLink {
			float: right;
			line-height: 30rpx;
			width: 110rpx;
			margin: 8rpx;
			padding: 1rpx !important;
			padding-top: 20rpx !important;

			color: #007aff;
			font-size: 26rpx;
		}
		.croppingLink {
           float: right;
           line-height: 30rpx;
           width: 110rpx;
           margin: 8rpx;
           padding: 1rpx !important;
           padding-top: 20rpx !important;
           color: #007aff;
           font-size: 26rpx;
        }

		.style {
			background-color: $uni-bg-color;
			padding: 20rpx;
			border-radius: $uni-border-radius-lg;
			font-size: 28rpx;

			&-list {
				.item {
					margin-top: 20rpx;

					.input {
						border-radius: $uni-border-radius-lg;
						font-size: 28rpx;
						height: 64rpx;
						padding: 0 20rpx;
						background-color: $uni-bg-color-grey;

						.value {
							display: flex;
							align-items: center;
							justify-content: space-between;
							min-height: 60rpx;

							&::after {
								font-family: 'iconfont';
								content: '\e840';
								color: $uni-text-color-grey;
							}

							&:empty::before {
								content: attr(data-tips);
								color: gray;
							}
						}
					}
				}
			}
		}

		.size {
			background-color: $uni-bg-color;
			padding: 20rpx;
			margin-top: 20rpx;
			border-radius: $uni-border-radius-lg;
			font-size: 28rpx;

			&-list {
				margin-top: 20rpx;
				display: grid;
				grid-template-columns:0.2fr 0.3fr 1fr 1fr 64rpx;
				// grid-template-columns: 0.2fr 0.3fr 1fr 1fr 64rpx;
				gap: 20rpx;

				.input {
					border-radius: $uni-border-radius-lg;
					font-size: 28rpx;
					height: 64rpx;
					padding: 0 20rpx;
					background-color: $uni-bg-color-grey;
				}
				.checkbox{
					border-radius: $uni-border-radius-lg;
					font-size: 28rpx;
					height: 64rpx;
					padding: 10rpx 0rpx;
				}
			}
			&-list1 {
				margin-top: 20rpx;
				display: grid;
				grid-template-columns:1fr 1fr 64rpx;
				gap: 20rpx;

				.input {
					border-radius: $uni-border-radius-lg;
					font-size: 28rpx;
					height: 64rpx;
					padding: 0 20rpx;
					background-color: $uni-bg-color-grey;
				}
				.checkbox{
					border-radius: $uni-border-radius-lg;
					font-size: 28rpx;
					height: 64rpx;
					padding: 10rpx 0rpx;
				}
			}
			.add {
				display: flex;
				margin-top: 20rpx;
			}

			.sizeContent {
				max-height: 42vh;
				overflow-y: scroll;
			}
		}
	}

	.submit {
		position: fixed;
		left: 20rpx;
		right: 20rpx;
		bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
		bottom: calc(env(safe-area-inset-bottom) + 20rpx);
		background-color: $uni-color-primary;
		color: $uni-text-color-inverse;
		height: 80rpx;
	}
.submit1 {
		left: 20rpx;
		right: 20rpx;
		background-color: $uni-color-primary;
		color: $uni-text-color-inverse;
		height: 80rpx;
		width: 250rpx;
	}.submit2 {
		left: 20rpx;
		right: 20rpx;
		// background-color: $uni-color-primary;
		// color: $uni-text-color-inverse;
		height: 80rpx;
		width: 250rpx;
	}
	.detailContent {
		background-color: $uni-bg-color;
		.choseColor{
			justify-content:space-between !important;
		}
		.title {
			display: flex;
			align-items: center;
			justify-content:center;// space-between;
			height: 80rpx;
			padding: 0 0rpx;
			border-bottom: 1rpx solid $uni-border-color;
			font-weight: bold;

			.txt {
				margin-left: 15rpx;
			}

			.status {
				font-size: 26rpx;
				font-weight: normal;

				&.success {
					color: $uni-color-primary;
				}

				&.fail {
					color: $uni-color-error;
				}
			}
		}

		.cont {
			.jobs {
				font-size: 28rpx;

				.row {
					line-height: 78rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					;

					&:nth-child(odd) {
						background-color: #f5f9ff;
					}

					.cell {
						&:nth-child(1) {
							width: 180rpx;
						}

						&:nth-child(2) {
							flex: 1;
							text-align: left;
						}

						.item {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.child {
								padding: 10rpx 6rpx;
								display: flex;
								align-items: center;
								justify-content: center;

								&:nth-child(1),
								&:nth-child(2) {
									width: 100rpx;
								}

								&:nth-child(3) {
									flex: 1;
								}

								.name {
									flex: 1;

									.primary {
										color: $uni-color-primary;
									}
								}
							}
						}
					}
				}
			}
		}

	}

	.org {
		color: red !important;
	}

	.checklist-group {
		padding: 10px 20px !important;
	}
	.modelBtnList{
		// width: 100%;
	}
	.btnModel{
		height: 50rpx !important;
		width: 70rpx !important;
		margin-left: 5rpx !important;
		font-size: 22rpx;
	}
	.detail1 {
		max-height:60vh;
		padding: 0rpx 20rpx 20rpx 20rpx;
		background-color: $uni-bg-color;
		.item{
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 0.625rem;
			min-height: 4rem;
			padding: 0 0.625rem;
			
			.input{
				border: 1px gray solid;
				height: 76rpx;
				width: 100%;
				border-radius: 10rpx;
			}
			.submit{
				width:100%;
				height: 76rpx;
				color: white;
				background-color: #007aff;
			}
			.submit1{
				width:100%;
				height: 76rpx;
				color: white;
				background-color: #007aff;
			}
		}
		
		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 80rpx;
			padding: 0 0rpx;
			border-bottom: 1rpx solid $uni-border-color;
			font-weight: bold;
			.status {
				font-size: 26rpx;
				font-weight: normal;
				&.success {
					color: $uni-color-primary;
				}
				&.fail {
					color: $uni-color-error;
				}
			}
		}
		.cont {
			.jobs {
				font-size: 28rpx;
				.scrollView{
					max-height: 79vh;
					padding: 20rpx 20rpx 20rpx 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}
				.row {
					padding: 20rpx 20rpx 20rpx 20rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
					&:nth-child(odd) {
						background-color: #f5f9ff;
					}
					.cell {
						&:nth-child(1) {
							width: 600rpx;
						}
						&:nth-child(2) {
							flex: 1;
						}
						.item {
							display: flex;
							align-items: center;
							justify-content: space-between;
							.child {
								padding: 10rpx 6rpx;
								display: flex;
								align-items: center;
									justify-content: center;
								width: 600rpx;
								.name {
									flex: 1;
									.primary {
										color: $uni-color-primary;
									}
								}
							}
						}
					}
				}
			}
		}
		
	}
</style>