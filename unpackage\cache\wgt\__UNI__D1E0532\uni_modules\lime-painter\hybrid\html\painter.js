!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Painter={})}(this,(function(t){"use strict";var e=function(){return(e=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}).apply(this,arguments)};function i(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))}function n(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var r={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},o="width",s="height",a="margin",h="center",d="flex-start",l="flex-end",c=["Top","Right","Bottom","Left"],f="left",u="right",p="top",g="bottom",v="middle",y=["contentSize","clientSize","borderSize","offsetSize"],x="row",b="column",w={TOP:p,MIDDLE:v,BOTTOM:g},m={LEFT:f,CENTER:h,RIGHT:u},S="view",z="text",I="image",M="qrcode",B="block",k="inline-block",W="none",P="flex",O="absolute",T="fixed",L="transparent",R="fill",F={display:B,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",flexDirection:x,flexWrap:"nowrap",textAlign:f,alignItems:d,justifyContent:d,position:"static",transformOrigin:"center center"},A={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth,screenHeight:window.innerHeight}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,r=new Image;r.onload=function(){i({width:r.naturalWidth,height:r.naturalHeight,path:r.src,src:e})},r.onerror=n,r.src=e}},E="object",C="undefined",j=typeof window==E?typeof uni==C||typeof uni!==C&&!uni.addInterceptor?r.WEB:r.H5:typeof swan==E?r.MP_BAIDU:typeof tt==E?r.MP_TOUTIAO:typeof plus===E?r.PLUS:typeof wx==E?r.MP_WEIXIN:void 0,H=j==r.MP_WEIXIN?wx:typeof uni!=C?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,A):typeof window!=C?A:uni;if(!H.upx2px){var D=((H.getSystemInfoSync&&H.getSystemInfoSync()).screenWidth||375)/750;H.upx2px=function(t){return D*t}}function Y(t){return/^-?\d+(\.\d+)?$/.test(t)}function $(t,e,i){if(Y(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|vw|vh|px|%)$/g.exec(t);if(!t||!n)return 0;var r=n[3];t=parseFloat(t);var o=0;if("rpx"===r)o=H.upx2px(t);else if("px"===r)o=1*t;else if("%"===r&&e)o=t*$(e)/100;else if("em"===r&&e)o=t*$(e||14);else if(["vw","vh"].includes(r)){var s=H.getSystemInfoSync(),a=s.screenWidth,h=s.screenHeight;o=t*("vw"==r?a:h)/100}return 1*o.toFixed(2)}return 0}function U(t){return/%$/.test(t)}var N=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},_=function(t,e,i,n,r,o){t.startsWith("linear")?function(t,e,i,n,r,o){for(var s=function(t,e,i,n,r){void 0===n&&(n=0);void 0===r&&(r=0);var o=t.match(/([-]?\d{1,3})deg/),s=o&&o[1]?parseFloat(o[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+n,y0:i+r,x1:Math.round(e/2)+n,y1:r};if(180===s)return{x0:Math.round(e/2)+n,y0:r,x1:Math.round(e/2)+n,y1:i+r};if(90===s)return{x0:n,y0:Math.round(i/2)+r,x1:e+n,y1:Math.round(i/2)+r};if(270===s)return{x0:e+n,y0:Math.round(i/2)+r,x1:n,y1:Math.round(i/2)+r};var a=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===a)return{x0:n,y0:i+r,x1:e+n,y1:r};if(s===180-a)return{x0:n,y0:r,x1:e+n,y1:i+r};if(s===180+a)return{x0:e+n,y0:r,x1:n,y1:i+r};if(s===360-a)return{x0:e+n,y0:i+r,x1:n,y1:r};var h=0,d=0,l=0,c=0;if(s<a||s>180-a&&s<180||s>180&&s<180+a||s>360-a){var f=s*Math.PI/180,u=s<a||s>360-a?i/2:-i/2,p=Math.tan(f)*u,g=s<a||s>180-a&&s<180?e/2-p:-e/2-p;h=-(l=p+(v=Math.pow(Math.sin(f),2)*g)),d=-(c=u+v/Math.tan(f))}if(s>a&&s<90||s>90&&s<90+a||s>180+a&&s<270||s>270&&s<360-a){var v;f=(90-s)*Math.PI/180,p=s>a&&s<90||s>90&&s<90+a?e/2:-e/2,u=Math.tan(f)*p,g=s>a&&s<90||s>270&&s<360-a?i/2-u:-i/2-u;h=-(l=p+(v=Math.pow(Math.sin(f),2)*g)/Math.tan(f)),d=-(c=u+v)}return h=Math.round(h+e/2)+n,d=Math.round(i/2-d)+r,l=Math.round(l+e/2)+n,c=Math.round(i/2-c)+r,{x0:h,y0:d,x1:l,y1:c}}(r,t,e,i,n),a=s.x0,h=s.y0,d=s.x1,l=s.y1,c=o.createLinearGradient(a,h,d,l),f=r.match(/linear-gradient\((.+)\)/)[1],u=X(f.substring(f.indexOf(",")+1)),p=0;p<u.colors.length;p++)c.addColorStop(u.percents[p],u.colors[p]);o.setFillStyle(c)}(e,i,n,r,t,o):t.startsWith("radial")&&function(t,e,i,n,r,o){for(var s=X(r.match(/radial-gradient\((.+)\)/)[1]),a=Math.round(t/2)+i,h=Math.round(e/2)+n,d=o.createRadialGradient(a,h,0,a,h,Math.max(t,e)/2),l=0;l<s.colors.length;l++)d.addColorStop(s.percents[l],s.colors[l]);o.setFillStyle(d)}(e,i,n,r,t,o)};function X(t){for(var e=[],i=[],n=0,r=t.substring(0,t.length-1).split("%,");n<r.length;n++){var o=r[n];e.push(o.substring(0,o.lastIndexOf(" ")).trim()),i.push(o.substring(o.lastIndexOf(" "),o.length)/100)}return{colors:e,percents:i}}function q(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function G(){return(G=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function V(t,e){return(V=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function J(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function Q(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return J(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?J(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Z(t){return"number"==typeof t}function K(t){return"auto"===t||null===t}function et(t){return/%$/.test(t)}var it=I,nt=z,rt=S,ot=M,st=k,at=O,ht=T;function dt(t){return t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()}))}function lt(t,e){var i,n,r=function(t){var e=t.match(/([a-z]+)/)[1];return[e,dt(t.split(e)[1])]}(t),o=r[0],s=r[1],a=e.split(" ");if(s)return(i={})[o+s]=e,i;if(a.length&&!s){var h=a[0],d=a[1],l=a[2],f=a[3];return(n={})[o+c[0]]=h,n[o+c[1]]=d||h,n[o+c[2]]=l||h,n[o+c[3]]=f||d||h,n}}function ct(t){t=t.trim();for(var e=new Array,i="+",n="",r=t.length,o=0;o<r;++o){if("."===t[o]||!isNaN(Number(t[o]))&&" "!==t[o])n+=t[o];else if("("===t[o]){for(var s=1,a=o;s>0;)"("===t[a+=1]&&(s+=1),")"===t[a]&&(s-=1);n=""+ct(t.slice(o+1,a)),o=a}if(isNaN(Number(t[o]))&&"."!==t[o]||o===r-1){var h=parseFloat(n);switch(i){case"+":e.push(h);break;case"-":e.push(-h);break;case"*":e.push(e.pop()*h);break;case"/":e.push(e.pop()/h)}i=t[o],n=""}}for(var d=0;e.length;)d+=e.pop();return d}var ft,ut=0,pt=function(){function t(){q(this,"elements",[]),q(this,"afterElements",[]),q(this,"beforeElements",[]),q(this,"ids",[]),q(this,"width",0),q(this,"height",0),q(this,"top",0),q(this,"left",0),q(this,"pre",null),q(this,"offsetX",0),q(this,"offsetY",0),ut++,this.id=ut}var e=t.prototype;return e.fixedBind=function(t,e){void 0===e&&(e=0),this.container=e?t.parent:t.root,this.container.fixedLine=this,this.fixedAdd(t)},e.fixedAdd=function(t){if(!this.ids.includes(t.id)){this.ids.push(t.id),this.elements.push(t);var e=t.computedStyle.zIndex;(void 0===e?0:e)>=0?this.afterElements.push(t):this.beforeElements.push(t),this.refreshLayout()}},e.bind=function(t){this.container=t.parent,this.container.line=null,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),this.isInline=t.isInline(),this.container.line=this,this.outerWidth=t.parent&&t.parent.contentSize.width?t.parent.contentSize.width:1/0,this.add(t)},e.getPreLine=function(){return this.container.lines[this.container.lines.length-2]},e.canIEnter=function(t){return!((100*t.offsetSize.width+100*this.width)/100>this.outerWidth)||(this.closeLine(),!1)},e.closeLine=function(){delete this.container.line},e.add=function(t){this.ids.includes(t.id)||(this.ids.push(t.id),this.elements.push(t),this.refreshWidthHeight(t))},e.refreshWidthHeight=function(t){t.offsetSize.height>this.height&&(this.height=t.offsetSize.height),this.width+=t.offsetSize.width||0,(this.container.lineMaxWidth||0)<this.width&&(this.container.lineMaxWidth=this.width)},e.refreshXAlign=function(){if(this.isInline){var t=this.container.contentSize.width-this.width,e=this.container.style.textAlign;e===h?t/=2:e===f&&(t=0),this.offsetX=t}},e.getOffsetY=function(t){if(!t||!t.style)return 0;var e=(t.style||{}).verticalAlign;return e===g?this.height-t.contentSize.height:e===v?(this.height-t.contentSize.height)/2:0},e.setIndent=function(t){var e=t.style.textIndent;if(e&&/^calc/.test(e)){var i=/^calc\((.+)\)$/.exec(e);if(i&&i[1]){var n=ct(i[1].replace(/([^\s\(\+\-\*\/]+)\.(left|right|bottom|top|width|height)/g,(function(e){var i=e.split("."),n=i[0],r=i[1],o=t.parent.querySelector(n);if(o&&o.offsetSize){var s={right:o.offsetSize.left+o.offsetSize.width,bottom:o.offsetSize.top+o.offsetSize.height};return o.offsetSize[r]||s[r]||0}})).replace(new RegExp(/-?[0-9]+(\.[0-9]+)?(rpx|px|%)/,"g"),$));t.style.textIndent=n}}},e.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){i.setIndent(t);var n=i.elements[e-1],r=i.getOffsetY(t);t.style.top=i.top+r,t.style.left=n?n.offsetSize.left+n.offsetSize.width:i.left,t.getBoxPosition()}))},e.refreshLayout=function(){this.afterElements=this.afterElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex})),this.beforeElements=this.beforeElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex}))},t}(),gt="lineMaxHeight",vt="lineMaxWidth",yt=((ft={})[x]={width:o,contentWidth:o,lineMaxWidth:vt,left:f,top:p,height:s,lineMaxHeight:gt,marginLeft:"marginLeft"},ft[b]={width:s,contentWidth:s,lineMaxWidth:vt,left:p,top:f,height:o,lineMaxHeight:gt,marginLeft:"marginTop"},ft),xt=function(t){var e,i;function n(){var e;return q(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call(this)||this),"outerWidth",0),e.exactValue=0,e.flexTotal=0,e.width=0,e.key=null,e.flexDirection="row",e}i=t,(e=n).prototype=Object.create(i.prototype),e.prototype.constructor=e,V(e,i);var r=n.prototype;return r.bind=function(t){this.container=t.parent,this.container.line=this,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),t.parent&&(this.flexDirection=t.parent.style.flexDirection,this.key=yt[this.flexDirection]),this.initHeight(t),this.outerWidth=t.parent&&t.parent.contentSize[this.key.contentWidth]?t.parent.contentSize[this.key.contentWidth]:1/0,this.add(t)},r.add=function(t){this.ids.push(t.id);var e=t.style.flex;Z(e)?this.flexTotal+=e:Z(this.getWidth(t.style))&&(this.exactValue+=this.getWidth(t.offsetSize)),this.elements.push(t),this.refreshWidthHeight(t),t.next||this.closeLine()},r.closeLine=function(){this.calcFlex()},r.initHeight=function(t){this[this.key.height]=0},r.getWidth=function(t){return t[this.key.width]||0},r.getHeight=function(t){return t[this.key.height]||0},r.setWidth=function(t,e){t[this.key.width]=e},r.setHeight=function(t,e){t[this.key.height]=e},r.calcFlex=function(){var t=this,e=this.container.contentSize[this.key.contentWidth],i=0;this.elements.forEach((function(n){var r=n.style,o=n.contentSize,s=t.getWidth(r)||t.getWidth(o);Z(r.flex)&&(s=r.flex/t.flexTotal*(e-t.exactValue)),t.setWidth(n.computedStyle,s),n.isFlexCalc=!0,delete n.line,delete n.lines,delete n.lineMaxWidth,n.getBoxWidthHeight(),i=Math.max(i,t.getHeight(n.offsetSize))})),this.setHeight(this,i)},r.refreshWidthHeight=function(t){var e=this.container.style.alignItems;e&&!t.style.alignSelf&&(t.style.alignSelf=e);var i=this.getHeight(t.offsetSize);i>this[this.key.height]&&(this.container[this.key.lineMaxHeight]=this[this.key.height]=i),this[this.key.width]+=this.getWidth(t.offsetSize);var n=Math.min(this.getWidth(this),!this.getWidth(this.container.contentSize)&&1/0);(this.container[this.key.lineMaxWidth]||0)<n&&(this.container[this.key.lineMaxWidth]=n)},r.refreshXAlign=function(){var t=this,e=this.elements.reduce((function(e,i){return e+t.getWidth(i.offsetSize)}),0),i=(this.outerWidth==1/0?0:this.outerWidth-e)||0,n=this.container.style.justifyContent;n===h?i/=2:n===d?i=0:["space-between","space-around"].includes(n)&&(!function(e,i){void 0===i&&(i=0),i/=t.elements.length+(e?-1:1),t.elements.forEach((function(n,r){var o;e&&!r||(n.style.margin?n.style.margin[t.key.marginLeft]+=i:n.style.margin=((o={})[t.key.marginLeft]=i,o),n.getBoxPosition())})),i=0}("space-between"==n,i),i=0),this.offsetX=i||0,this.refreshYAlign()},r.refreshYAlign=function(){var t=this;if(1==this.container.lines.length)return 0;var e=this.container.lines.reduce((function(e,i){return e+t.getHeight(i)}),0),i=this.container.style.alignItems,n=this.getHeight(this.container.contentSize);if(i===h){var r=(n-e)/(this.container.lines.length+1);this.container.lines.forEach((function(t){t.offsetY=r}))}if(i===l){var o=n-e;this.container.lines[0].offsetY=o}},r.getOffsetY=function(t){if(this.container.lines.length>1)return 0;var e=t.style.alignSelf,i=this.getHeight(this.container.contentSize),n=i-this.getHeight(t.offsetSize);return e===l?n:e===h?n/2:"stretch"===e?(n&&t.name==S&&(t.style[this.key.width]=this.getWidth(t.offsetSize),t.style[this.key.height]=i,delete t.line,delete t.lines,t.getBoxWidthHeight()),0):0},r.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){i.setIndent(t);var n=i.elements[e-1],r=i.getOffsetY(t);t.style[i.key.top]=i[i.key.top]+r,t.style[i.key.left]=n?n.offsetSize[i.key.left]+i.getWidth(n.offsetSize):i[i.key.left],t.getBoxPosition()}))},n}(pt),bt=I,wt=z,mt=S,St=B,zt=k,It=P,Mt=O,Bt=T,kt=0,Wt={left:null,top:null,width:null,height:null},Pt=new Map,Ot=function(){function t(t,e,i,n){var r=this;q(this,"id",kt++),q(this,"style",{left:null,top:null,width:null,height:null}),q(this,"computedStyle",{}),q(this,"originStyle",{}),q(this,"children",{}),q(this,"layoutBox",G({},Wt)),q(this,"contentSize",G({},Wt)),q(this,"clientSize",G({},Wt)),q(this,"borderSize",G({},Wt)),q(this,"offsetSize",G({},Wt)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.type||t.name,this.attributes=this.getAttributes(t);var s=function(t,e){var i,n=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],r=t.type,s=void 0===r?rt:r,h=t.styles,d=void 0===h?{}:h,l=(e||{}).computedStyle,f=Object.assign({},F);if([nt,it,ot].includes(s)&&!d.display&&(f.display=st),l)for(var u=0;u<n.length;u++){var p=n[u];(d[p]||l[p])&&(d[p]=d[(i=p,i.replace(/([A-Z])/g,"-$1").toLowerCase())]||d[p]||l[p])}for(var g=function(t){var e,i,n,r,h=d[t];if(/-/.test(t)&&(t=dt(t),f[t]=h),/^(box|text)?shadow$/i.test(t)){var l=[];return h.replace(/((-?\d+(rpx|px|vw|vh)?\s+?){3})(.+)/,(function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];l=t[1].match(/-?\d+(rpx|px|vw|vh)?/g).map((function(t){return $(t)})).concat(t[4])})),/^text/.test(t)?f.textShadow=l:f.boxShadow=l,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var u=t.match(/^border([BTRLa-z]+)?/)[0],p=t.match(/[W|S|C][a-z]+/),g=h.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?$(t,""):t}));return f[u]||(f[u]={}),1==g.length&&p?f[u][u+p[0]]=g[0]:f[u]=((e={})[u+o]=Y(g[0])?g[0]:0,e[u+"Style"]=g[1]||"solid",e[u+"Color"]=g[2]||"black",e),"continue"}if(/^background(color)?$/i.test(t))return f.backgroundColor=h,"continue";if(/^objectPosition$/i.test(t))return f[t]=h.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var v=/radius$/i.test(t),y="borderRadius",x=v?y:t.match(/[a-z]+/)[0],b=function(t,e){return"border"+t+e+"Radius"},w=[0,0,0,0].map((function(t,e){return v?[b(c[0],c[3]),b(c[0],c[1]),b(c[2],c[1]),b(c[2],c[3])][e]:x+c[e]}));if("padding"===t||t===a||/^(border)?radius$/i.test(t)){g=(""+h).split(" ").map((function(e){return/^-?\d+(rpx|px|vh|vw)?$/.test(e)?$(e):t!=a&&/auto/.test(e)?0:e}),[])||[0];var m=v?y:t,S=g[0],z=g[1],I=g[2],M=g[3];f[m]=((i={})[w[0]]=K(S)?0:S,i[w[1]]=Y(z)||K(z)?z:S,i[w[2]]=K(Y(I)?I:S)?0:Y(I)?I:S,i[w[3]]=Y(M)?M:z||S,i)}else"object"==typeof f[x]||(f[x]=((n={})[w[0]]=f[x]||0,n[w[1]]=f[x]||0,n[w[2]]=f[x]||0,n[w[3]]=f[x]||0,n)),f[x][t]=x==a&&K(h)||et(h)?h:$(h);return"continue"}if(/^transform$/i.test(t))return f[t]={},h.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var r=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),o=function(t,e){return t.includes("deg")?1*t:e&&!et(e)?$(t,e):t};i.includes("matrix")?f[t][i]=r.map((function(t){return 1*t})):i.includes("rotate")?f[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?f[t][i]=/[X]/.test(i)?o(r[0],d.width):o(r[0],d.height):(f[t][i+"X"]=o(r[0],d.width),f[t][i+"Y"]=o(r[1]||r[0],d.height))})),"continue";if(/^font$/i.test(t)&&console.warn("font 不支持简写"),/^textindent/i.test(t)&&(f[t]=/^calc/.test(h)?h:$(h)),/^textstroke/i.test(t)){var B=t.match(/color|width|type$/i),k=(u="textStroke",h.split(" ").map((function(t){return/^\d+(rpx|px|vh|vw)?$/.test(t)?$(t):t})));return B?f[u]?f[u][B[0]]=k[0]:f[u]=((r={})[B[0]]=k[0],r):f[u]={width:k[0],color:k[1],type:k[2]},"continue"}/^left|top$/i.test(t)&&![at,ht].includes(d.position)?f[t]=0:f[t]=/^-?[\d\.]+(px|rpx|vw|vh)?$/.test(h)?$(h):/em$/.test(h)&&s==nt?$(h,d.fontSize):h},v=0,y=Object.keys(d);v<y.length;v++)g(y[v]);return f}(t,e);this.isAbsolute=s.position==Mt,this.isFixed=s.position==Bt,this.originStyle=s,Object.keys(s).forEach((function(t){Object.defineProperty(r.style,t,{configurable:!0,enumerable:!0,get:function(){return s[t]},set:function(e){s[t]=e}})}));var h={contentSize:G({},this.contentSize),clientSize:G({},this.clientSize),borderSize:G({},this.borderSize),offsetSize:G({},this.offsetSize)};Object.keys(h).forEach((function(t){Object.keys(r[t]).forEach((function(e){Object.defineProperty(r[t],e,{configurable:!0,enumerable:!0,get:function(){return h[t][e]},set:function(i){h[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.prev=function(t){void 0===t&&(t=this);var e=t.parent.getChildren();return e[e.findIndex((function(e){return e.id==t.id}))-1]},e.querySelector=function(t){var e=this.getChildren();if("string"!=typeof t)return null;var i=e.find((function(e){var i=e.id,n=e.attributes;return i==t||n&&n.uid==t}));return i||(this.parent&&this.parent.querySelector&&this.parent.querySelector(t)||null)},e.getLineRect=function(t,e){var i={width:0,height:0},n=e?e.lines:this.parent&&this.parent.lines;return n&&n.find((function(e){return e.ids.includes(t)}))||i},e.setPosition=function(t,e){var i={left:o,top:s,right:o,bottom:s};Object.keys(i).forEach((function(n){var r=n==u?f:p;[u,g].includes(n)&&void 0!==t.style[n]&&!Y(t.originStyle[r])?t.style[r]=e[i[n]]-t.offsetSize[i[n]]-$(t.style[n],e[i[n]]):t.style[n]=$(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes,i=void 0===e?{}:e,n=t.uid,r=t.url,o=t.src,s=t.replace,a=t.text;return n&&(i.uid=n),(r||o)&&(i.src=i.src||r||o),s&&(i.replace=s),a&&(i.text=a),i},e.getOffsetSize=function(t,e,i){void 0===i&&(i=y[3]);var n=e||{},r=n.margin,o=(r=void 0===r?{}:r).marginLeft,s=void 0===o?0:o,a=r.marginTop,h=void 0===a?0:a,d=r.marginRight,l=void 0===d?0:d,c=r.marginBottom,f=void 0===c?0:c,u=n.padding,p=(u=void 0===u?{}:u).paddingLeft,g=void 0===p?0:p,v=u.paddingTop,x=void 0===v?0:v,b=u.paddingRight,w=void 0===b?0:b,m=u.paddingBottom,S=void 0===m?0:m,z=n.border,I=(z=void 0===z?{}:z).borderWidth,M=void 0===I?0:I,B=n.borderTop,k=(B=void 0===B?{}:B).borderTopWidth,W=void 0===k?M:k,P=n.borderBottom,O=(P=void 0===P?{}:P).borderBottomWidth,T=void 0===O?M:O,L=n.borderRight,R=(L=void 0===L?{}:L).borderRightWidth,F=void 0===R?M:R,A=n.borderLeft,E=(A=void 0===A?{}:A).borderLeftWidth,C=void 0===E?M:E,j=s<0&&l<0?Math.abs(s+l):0,H=h<0&&f<0?Math.abs(h+f):0,D=s>=0&&l<0,Y=h>=0&&f<0;return i==y[0]&&(this[i].left=t.left+s+g+C+(D?2*-l:0),this[i].top=t.top+h+x+W+(Y?2*-f:0),this[i].width=t.width+(this[i].widthAdd?0:j),this[i].height=t.height+(this[i].heightAdd?0:H),this[i].widthAdd=j,this[i].heightAdd=H),i==y[1]&&(this[i].left=t.left+s+C+(D<0?-l:0),this[i].top=t.top+h+W+(Y?-f:0),this[i].width=t.width+g+w,this[i].height=t.height+x+S),i==y[2]&&(this[i].left=t.left+s+C/2+(D<0?-l:0),this[i].top=t.top+h+W/2+(Y?-f:0),this[i].width=t.width+g+w+C/2+F/2,this[i].height=t.height+x+S+T/2+W/2),i==y[3]&&(this[i].left=t.left+(D<0?-l:0),this[i].top=t.top+(Y?-f:0),this[i].width=t.width+g+w+C+F+s+l,this[i].height=t.height+x+S+T+W+f+h),this[i]},e.layoutBoxUpdate=function(t,e,i,n){var r=this;if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var o=e||{},s=o.border,a=(s=void 0===s?{}:s).borderWidth,h=void 0===a?0:a,d=o.borderTop,l=(d=void 0===d?{}:d).borderTopWidth,c=void 0===l?h:l,f=o.borderBottom,u=(f=void 0===f?{}:f).borderBottomWidth,p=void 0===u?h:u,g=o.borderRight,v=(g=void 0===g?{}:g).borderRightWidth,x=void 0===v?h:v,b=o.borderLeft,w=(b=void 0===b?{}:b).borderLeftWidth,m=void 0===w?h:w,S=o.padding,z=(S=void 0===S?{}:S).paddingTop,I=void 0===z?0:z,M=S.paddingRight,B=void 0===M?0:M,k=S.paddingBottom,W=void 0===k?0:k,P=S.paddingLeft,O=void 0===P?0:P;i||(t.width-=O+B+x+m),1!==i||n||(t.height-=I+W+c+p)}this.layoutBox&&(y.forEach((function(i){return r.layoutBox[i]=r.getOffsetSize(t,e,i)})),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},e.getBoxPosition=function(){var t=this.computedStyle,e=this.fixedLine,i=this.lines,n=t.left,r=void 0===n?0:n,o=t.top,s=void 0===o?0:o,a=t.padding||{},h=a.paddingBottom,d=void 0===h?0:h,l=a.paddingRight,c=void 0===l?0:l,f=G({},this.contentSize,{left:r,top:s}),u=this.contentSize.top-this.offsetSize.top,p=this.contentSize.left-this.offsetSize.left;if(this.root.fixedLine&&!this.root.isDone){this.root.isDone=!0;for(var g,v=Q(this.root.fixedLine.elements);!(g=v()).done;){var y=g.value;y.setPosition(y,this.root.offsetSize),y.getBoxPosition()}}if(e)for(var x,b=Q(e.elements);!(x=b()).done;){var w=x.value;w.setPosition(w,f),w.style.left+=r+p+c,w.style.top+=s+u+d,w.getBoxPosition()}if(i)for(var m,S=Q(i);!(m=S()).done;){m.value.layout(f.top+u,f.left+p)}return this.layoutBoxUpdate(f,t),this.layoutBox},e.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},e.isBlock=function(t){return void 0===t&&(t=this),t&&t.style.display==St},e.isFlex=function(t){return void 0===t&&(t=this),t&&t.style.display==It},e.isInFlow=function(){return!(this.isAbsolute||this.isFixed)},e.inFlexBox=function(t){return void 0===t&&(t=this),!!t.isInFlow()&&(!!t.parent&&(!(!t.parent||t.parent.style.display!==It)||void 0))},e.isInline=function(t){return void 0===t&&(t=this),t&&t.style.display==zt},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t),n=i.width,r=i.actualBoundingBoxAscent,o=i.actualBoundingBoxDescent;return{ascent:r,descent:o,width:n,fontHeight:r+o||.7*e+1}},e.getParentSize=function(t,e){if(void 0===t&&(t=this),void 0===e&&(e=!1),t&&t.parent){if(t.parent.contentSize.width)return t.parent.contentSize;if(e)return this.getParentSize(t.parent,e)}return null},e.getBoxWidthHeight=function(){var t=this,e=this.name,i=this.computedStyle,n=this.attributes,r=this.parent,o=void 0===r?{}:r,s=this.ctx,a=this.getChildren(),h=i.left,d=void 0===h?0:h,l=i.top,c=void 0===l?0:l,f=i.bottom,u=i.right,p=i.width,g=void 0===p?0:p,v=i.minWidth,y=i.maxWidth,x=i.minHeight,b=i.maxHeight,w=i.height,m=void 0===w?0:w,S=i.fontSize,z=i.fontWeight,I=i.fontFamily,M=i.fontStyle,B=i.position;i.textIndent;var k=i.lineClamp,W=i.lineHeight,P=i.padding,O=void 0===P?{}:P,T=i.margin,L=void 0===T?{}:T,R=i.border,F=(R=void 0===R?{}:R).borderWidth,A=void 0===F?0:F,E=i.borderRight,C=(E=void 0===E?{}:E).borderRightWidth,j=void 0===C?A:C,H=i.borderLeft,D=(H=void 0===H?{}:H).borderLeftWidth,Y=void 0===D?A:D,U=o.contentSize&&o.contentSize.width,N=o.contentSize&&o.contentSize.height;if(et(g)&&U&&(g=$(g,U)),et(g)&&!U&&(g=null),et(m)&&N&&(m=$(m,N)),et(m)&&!N&&(m=null),et(v)&&U&&(v=$(v,U)),et(y)&&U&&(y=$(y,U)),et(x)&&N&&(x=$(x,N)),et(b)&&N&&(b=$(b,N)),i.padding&&U)for(var _ in i.padding)Object.hasOwnProperty.call(O,_)&&(O[_]=$(O[_],U));var X=O.paddingRight,q=void 0===X?0:X,G=O.paddingLeft,V=void 0===G?0:G;if(i.margin&&[L.marginLeft,L.marginRight].includes("auto"))if(g){var J=U&&U-g-q-V-Y-j||0;L.marginLeft==L.marginRight?L.marginLeft=L.marginRight=J/2:K(L.marginLeft)?L.marginLeft=J:L.marginRight=J}else L.marginLeft=L.marginRight=0;var Q=L.marginRight,Z=void 0===Q?0:Q,tt=L.marginLeft,it={width:g,height:m,left:0,top:0},nt=V+q+Y+j+(void 0===tt?0:tt)+Z;if(this.offsetWidth=nt,e==wt&&!this.attributes.widths){var rt=n.text||"";s.save(),s.setFonts({fontFamily:I,fontSize:S,fontWeight:z,fontStyle:M}),rt.length,"\n"==rt&&(rt="",this.isBr=!0),(""+rt).split("\n").map((function(e){var i=Array.from(e).map((function(e){var i=""+(/^[\u4e00-\u9fa5]+$/.test(e)?"cn":e)+I+S+z+M,n=Pt.get(i);if(n)return{width:n,text:e};var r=t.measureText(e,S).width;return Pt.set(i,r),{width:r,text:e}})),n=t.measureText(e,S),r=n.fontHeight,o=n.ascent,s=n.descent;t.attributes.fontHeight=r,t.attributes.ascent=o,t.attributes.descent=s,t.attributes.widths||(t.attributes.widths=[]),t.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e.width}),0)})})),s.restore()}if(e==bt&&null==g){var ot=n.width,st=n.height;it.width=this.contrastSize(Math.round(ot*m/st)||0,v,y),this.layoutBoxUpdate(it,i,0)}if(e==wt&&null==g){var at=this.attributes.widths,ht=Math.max.apply(Math,at.map((function(t){return t.total})));if(o&&U>0&&(ht>U||this.isBlock(this))&&!this.isAbsolute&&!this.isFixed)ht=U;it.width=this.contrastSize(ht,v,y),this.layoutBoxUpdate(it,i,0)}if(e==wt&&(o.style.flex||!this.attributes.lines)){var dt=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e.width>it.width?(dt++,e.width):t+e.width}),0)})),dt=k&&dt>k?k:dt,this.attributes.lines=dt}if(e==bt&&null==m){var lt=n.width,ct=n.height;n.text,it.height=this.contrastSize($(it.width*ct/lt)||0,x,b),this.layoutBoxUpdate(it,i,1)}e==wt&&null==m&&(W=$(W,S),it.height=this.contrastSize($(this.attributes.lines*W),x,b),this.layoutBoxUpdate(it,i,1,!0)),!g&&o&&o.children&&U&&(!this.isFlex(o)||o.isFlexCalc)&&([mt,wt].includes(e)&&this.isFlex()||e==mt&&this.isBlock(this)&&this.isInFlow())&&(it.width=this.contrastSize(U-(o.isFlexCalc?0:nt),v,y),this.layoutBoxUpdate(it,i)),g&&!et(g)&&(it.width=this.contrastSize(g,v,y),this.layoutBoxUpdate(it,i,0)),m&&!et(m)&&(it.height=this.contrastSize(it.height,x,b),this.layoutBoxUpdate(it,i,1));var ft=0;if(a.length){var ut=null,gt=!1;a.forEach((function(e,n){e.getBoxWidthHeight();var r=a[n+1];if(r&&r.isInFlow()&&(e.next=r),e.isInFlow()&&!e.inFlexBox()){var o=t.getBoxState(ut,e);if(e.isBr)return gt=!0;t.line&&t.line.canIEnter(e)&&!o&&!gt?t.line.add(e):(gt=!1,(new pt).bind(e)),ut=e}else e.inFlexBox()?t.line&&(t.line.canIEnter(e)||"nowrap"==i.flexWrap)?t.line.add(e):(new xt).bind(e):e.isFixed?t.root.fixedLine?t.root.fixedLine.fixedAdd(e):(new pt).fixedBind(e):t.fixedLine?t.fixedLine.fixedAdd(e):(new pt).fixedBind(e,1)})),this.lines&&(ft=this.lines.reduce((function(t,e){return t+e.height}),0))}var vt=0,yt=0;if(!g&&(this.isAbsolute||this.isFixed)&&U){var St=B==Mt?U:this.root.width,zt=St-(et(d)?$(d,St):d)-(et(u)?$(u,St):u);vt=i.left?zt:this.lineMaxWidth}if(!m&&(null!=c?c:this.isAbsolute||this.isFixed&&N)){var It=B==Mt?N:this.root.height,Bt=It-(et(c)?$(c,It):c)-(et(f)?$(f,It):f);yt=i.top?Bt:0}if(g&&!et(g)||it.width||(it.width=vt||this.contrastSize((this.isBlock(this)&&!this.isInFlow()?U||o.lineMaxWidth:this.lineMaxWidth)||this.lineMaxWidth,v,y),this.layoutBoxUpdate(it,i,0)),m||!ft&&!yt||(it.height=yt||this.contrastSize(ft,x,b),this.layoutBoxUpdate(it,i)),i.borderRadius&&this.borderSize&&this.borderSize.width)for(var kt in i.borderRadius)Object.hasOwnProperty.call(i.borderRadius,kt)&&(i.borderRadius[kt]=$(i.borderRadius[kt],this.borderSize.width));return this.layoutBox},e.layout=function(){return this.getBoxWidthHeight(),this.root.offsetSize=this.offsetSize,this.root.contentSize=this.contentSize,this.getBoxPosition(),this.offsetSize},t}(),Tt=function(){var t,e,i,n,r,o,s=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],a=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],h=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],d=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],l=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],c=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],f=[],u=[],p=[],g=[],v=[],y=2;function x(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,g[i+=t]=1}function b(t,i){var n;for(p[t+e*i]=1,n=-2;n<2;n++)p[t+n+e*(i-2)]=1,p[t-2+e*(i+n+1)]=1,p[t+2+e*(i+n)]=1,p[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)x(t-1,i+n),x(t+1,i-n),x(t-n,i-1),x(t+n,i+1)}function w(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var m=[];function S(t,e,i,n){var r,o,s;for(r=0;r<n;r++)f[i+r]=0;for(r=0;r<e;r++){if(255!=(s=l[f[t+r]^f[i]]))for(o=1;o<n;o++)f[i+o-1]=f[i+o]^c[w(s+m[n-o])];else for(o=i;o<i+n;o++)f[o]=f[o+1];f[i+n-1]=255==s?0:c[w(s+m[0])]}}function z(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,g[i+=t]}function I(t){var i,n,r,o;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||z(i,n)||(p[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||z(i,n)||(p[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(r=0,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 3:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=o,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(r=0,o=n>>1&1,i=0;i<e;i++,r++)3==r&&(r=0,o=!o),o||z(i,n)||(p[i+n*e]^=1);break;case 5:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+!(!r|!o)||z(i,n)||(p[i+n*e]^=1);break;case 6:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+(r&&r==o)&1||z(i,n)||(p[i+n*e]^=1);break;case 7:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(r&&r==o)+(i+n&1)&1||z(i,n)||(p[i+n*e]^=1)}}function M(t){var e,i=0;for(e=0;e<=t;e++)v[e]>=5&&(i+=3+v[e]-5);for(e=3;e<t-1;e+=2)v[e-2]==v[e+2]&&v[e+2]==v[e-1]&&v[e-1]==v[e+1]&&3*v[e-1]==v[e]&&(0==v[e-3]||e+3>t||3*v[e-3]>=4*v[e]||3*v[e+3]>=4*v[e])&&(i+=40);return i}function B(){var t,i,n,r,o,s=0,a=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(p[t+e*i]&&p[t+1+e*i]&&p[t+e*(i+1)]&&p[t+1+e*(i+1)]||!(p[t+e*i]||p[t+1+e*i]||p[t+e*(i+1)]||p[t+1+e*(i+1)]))&&(s+=3);for(i=0;i<e;i++){for(v[0]=0,n=r=t=0;t<e;t++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,a+=(r=o)?1:-1;s+=M(n)}a<0&&(a=-a);var h=a,d=0;for(h+=h<<2,h<<=1;h>e*e;)h-=e*e,d++;for(s+=10*d,t=0;t<e;t++){for(v[0]=0,n=r=i=0;i<e;i++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,r=o;s+=M(n)}return s}var k=null;return{api:{get ecclevel(){return y},set ecclevel(t){y=t},get size(){return _size},set size(t){_size=t},get canvas(){return k},set canvas(t){k=t},getFrame:function(v){return function(v){var M,k,W,P,O,T,L,R;P=v.length,t=0;do{if(t++,W=4*(y-1)+16*(t-1),i=d[W++],n=d[W++],r=d[W++],o=d[W],P<=(W=r*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,O=r+(r+o)*(i+n)+n,P=0;P<O;P++)u[P]=0;for(f=v.slice(0),P=0;P<e*e;P++)p[P]=0;for(P=0;P<(e*(e+1)+1)/2;P++)g[P]=0;for(P=0;P<3;P++){for(W=0,k=0,1==P&&(W=e-7),2==P&&(k=e-7),p[k+3+e*(W+3)]=1,M=0;M<6;M++)p[k+M+e*W]=1,p[k+e*(W+M+1)]=1,p[k+6+e*(W+M)]=1,p[k+M+1+e*(W+6)]=1;for(M=1;M<5;M++)x(k+M,W+1),x(k+1,W+M+1),x(k+5,W+M),x(k+M+1,W+5);for(M=2;M<4;M++)p[k+M+e*(W+2)]=1,p[k+2+e*(W+M+1)]=1,p[k+4+e*(W+M)]=1,p[k+M+1+e*(W+4)]=1}if(t>1)for(P=s[t],k=e-7;;){for(M=e-7;M>P-3&&(b(M,k),!(M<P));)M-=P;if(k<=P+9)break;b(6,k-=P),b(k,6)}for(p[8+e*(e-8)]=1,k=0;k<7;k++)x(7,k),x(e-8,k),x(7,k+e-7);for(M=0;M<8;M++)x(M,7),x(M+e-8,7),x(M,e-8);for(M=0;M<9;M++)x(M,8);for(M=0;M<8;M++)x(M+e-8,8),x(8,M);for(k=0;k<7;k++)x(8,k+e-7);for(M=0;M<e-14;M++)1&M?(x(8+M,6),x(6,8+M)):(p[8+M+6*e]=1,p[6+e*(8+M)]=1);if(t>6)for(P=a[t-7],W=17,M=0;M<6;M++)for(k=0;k<3;k++,W--)1&(W>11?t>>W-12:P>>W)?(p[5-M+e*(2-k+e-11)]=1,p[2-k+e-11+e*(5-M)]=1):(x(5-M,2-k+e-11),x(2-k+e-11,5-M));for(k=0;k<e;k++)for(M=0;M<=k;M++)p[M+e*k]&&x(M,k);for(O=f.length,T=0;T<O;T++)u[T]=f.charCodeAt(T);if(f=u.slice(0),O>=(M=r*(i+n)+n)-2&&(O=M-2,t>9&&O--),T=O,t>9){for(f[T+2]=0,f[T+3]=0;T--;)P=f[T],f[T+3]|=255&P<<4,f[T+2]=P>>4;f[2]|=255&O<<4,f[1]=O>>4,f[0]=64|O>>12}else{for(f[T+1]=0,f[T+2]=0;T--;)P=f[T],f[T+2]|=255&P<<4,f[T+1]=P>>4;f[1]|=255&O<<4,f[0]=64|O>>4}for(T=O+3-(t<10);T<M;)f[T++]=236,f[T++]=17;for(m[0]=1,T=0;T<o;T++){for(m[T+1]=1,L=T;L>0;L--)m[L]=m[L]?m[L-1]^c[w(l[m[L]]+T)]:m[L-1];m[0]=c[w(l[m[0]]+T)]}for(T=0;T<=o;T++)m[T]=l[m[T]];for(W=M,k=0,T=0;T<i;T++)S(k,r,W,o),k+=r,W+=o;for(T=0;T<n;T++)S(k,r+1,W,o),k+=r+1,W+=o;for(k=0,T=0;T<r;T++){for(L=0;L<i;L++)u[k++]=f[T+L*r];for(L=0;L<n;L++)u[k++]=f[i*r+T+L*(r+1)]}for(L=0;L<n;L++)u[k++]=f[i*r+T+L*(r+1)];for(T=0;T<o;T++)for(L=0;L<i+n;L++)u[k++]=f[M+T+L*o];for(f=u,M=k=e-1,W=O=1,R=(r+o)*(i+n)+n,T=0;T<R;T++)for(P=f[T],L=0;L<8;L++,P<<=1){128&P&&(p[M+e*k]=1);do{O?M--:(M++,W?0!=k?k--:(W=!W,6==(M-=2)&&(M--,k=9)):k!=e-1?k++:(W=!W,6==(M-=2)&&(M--,k-=8))),O=!O}while(z(M,k))}for(f=p.slice(0),P=0,k=3e4,W=0;W<8&&(I(W),(M=B())<k&&(k=M,P=W),7!=P);W++)p=f.slice(0);for(P!=W&&I(P),k=h[P+(y-1<<3)],W=0;W<8;W++,k>>=1)1&k&&(p[e-1-W+8*e]=1,W<6?p[8+e*W]=1:p[8+e*(W+1)]=1);for(W=0;W<7;W++,k>>=1)1&k&&(p[8+e*(e-7+W)]=1,W?p[6-W+8*e]=1:p[7+8*e]=1);return p}(v)},utf16to8:function(t){var e,i,n,r;for(e="",n=t.length,i=0;i<n;i++)(r=t.charCodeAt(i))>=1&&r<=127?e+=t.charAt(i):r>2047?(e+=String.fromCharCode(224|r>>12&15),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|r>>0&63)):(e+=String.fromCharCode(192|r>>6&31),e+=String.fromCharCode(128|r>>0&63));return e},draw:function(t,i,n,r,o){i.drawView(n,r);var s=i.ctx,a=n.contentSize,h=a.width,d=a.height,l=a.left,c=a.top;r.borderRadius,r.backgroundColor;var f=r.color,u=void 0===f?"#000000":f;r.border,n.contentSize.left,n.borderSize.left,n.contentSize.top,n.borderSize.top;if(y=o||y,s){s.save(),i.setOpacity(r),i.setTransform(n,r);var p=Math.min(h,d);t=this.utf16to8(t);var g=this.getFrame(t),v=p/e;s.setFillStyle(u);for(var x=0;x<e;x++)for(var b=0;b<e;b++)g[b*e+x]&&s.fillRect(l+v*x,c+v*b,v,v);s.restore(),i.setBorder(n,r)}else console.warn("No canvas provided to draw QR code in!")}}}}(),Lt=I,Rt=z,Ft=M,At=S,Et=w.TOP,Ct=w.MIDDLE,jt=w.BOTTOM,Ht=m.LEFT,Dt=m.CENTER,Yt=m.RIGHT,$t=function(){function t(t){var e,i=this;this.v="1.9.5.1",this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.useCORS=!1,this.performance=!1,this.imageBus=[],this.createImage=function(t,e){return new Promise((function(n,r){var o=null;window||i.canvas.createImage?(o=i.canvas&&i.canvas.createImage?i.canvas.createImage():new Image,e&&o.setAttribute("crossOrigin","Anonymous"),o.src=t,o.onload=function(){n({width:o.naturalWidth||o.width,height:o.naturalHeight||o.height,path:o,src:this.src})},o.onerror=function(t){r(t)}):r({fail:"getImageInfo fail",src:t})}))},this.options=t,Object.assign(this,t),this.ctx=((e=t.context).setFonts=function(t){var i=t.fontFamily,n=void 0===i?"sans-serif":i,o=t.fontSize,s=void 0===o?14:o,a=t.fontWeight,h=void 0===a?"normal":a,d=t.fontStyle,l=void 0===d?"normal":d;j==r.MP_TOUTIAO&&(h="bold"==h?"bold":"",l="italic"==l?"italic":""),e.font=l+" "+h+" "+Math.round(s)+"px "+n},e.draw&&e.setFillStyle?e:Object.assign(e,{setStrokeStyle:function(t){e.strokeStyle=t},setLineWidth:function(t){e.lineWidth=t},setLineCap:function(t){e.lineCap=t},setFillStyle:function(t){e.fillStyle=t},setFontSize:function(t){e.font=String(t)+"px sans-serif"},setGlobalAlpha:function(t){e.globalAlpha=t},setLineJoin:function(t){e.lineJoin=t},setTextAlign:function(t){e.textAlign=t},setMiterLimit:function(t){e.miterLimit=t},setShadow:function(t,i,n,r){e.shadowOffsetX=t,e.shadowOffsetY=i,e.shadowBlur=n,e.shadowColor=r},setTextBaseline:function(t){e.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1,fixedLine:null},this.size=this.root;var n=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){n=t,i.lifecycle("onProgress",t/i.count)},get:function(){return n||0}})}return t.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},t.prototype.setContext=function(t){t&&(this.ctx=t)},t.prototype.init=function(){if(this.canvas.height||r.WEB==j){this.ctx.setTransform(1,0,0,1,0,0);var t=this.size.height*this.pixelRatio,e=this.size.width*this.pixelRatio;this.canvas.height=t,this.canvas.width=e,this.ctx.scale(this.pixelRatio,this.pixelRatio)}},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.size.width,this.size.height)},t.prototype.clipPath=function(t,e,i,n,r,o,s){void 0===o&&(o=!1),void 0===s&&(s=!1);var a=this.ctx;if(/polygon/.test(r)){var h=r.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];a.beginPath(),h.map((function(r){var o=r.split(" "),s=o[0],a=o[1];return[$(s,i)+t,$(a,n)+e]})).forEach((function(t,e){0==e?a.moveTo(t[0],t[1]):a.lineTo(t[0],t[1])})),a.closePath(),s&&a.stroke(),o&&a.fill()}},t.prototype.roundRect=function(t,e,i,n,r,o,s){if(void 0===o&&(o=!1),void 0===s&&(s=!1),!(r<0)){var a=this.ctx;if(a.beginPath(),r){var h=r||{},d=h.borderTopLeftRadius,l=void 0===d?r||0:d,c=h.borderTopRightRadius,f=void 0===c?r||0:c,u=h.borderBottomRightRadius,p=void 0===u?r||0:u,g=h.borderBottomLeftRadius,v=void 0===g?r||0:g;a.arc(t+i-p,e+n-p,p,0,.5*Math.PI),a.lineTo(t+v,e+n),a.arc(t+v,e+n-v,v,.5*Math.PI,Math.PI),a.lineTo(t,e+l),a.arc(t+l,e+l,l,Math.PI,1.5*Math.PI),a.lineTo(t+i-f,e),a.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),a.lineTo(t+i,e+n-p)}else a.rect(t,e,i,n);a.closePath(),s&&a.stroke(),o&&a.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,r=this.ctx,o=i||{},s=o.scaleX,a=void 0===s?1:s,h=o.scaleY,d=void 0===h?1:h,l=o.translateX,c=void 0===l?0:l,f=o.translateY,u=void 0===f?0:f,p=o.rotate,g=void 0===p?0:p,v=o.skewX,y=void 0===v?0:v,x=o.skewY,b=void 0===x?0:x,w=t.left,m=t.top,S=t.width,z=t.height;c=$(c,S)||0,u=$(u,z)||0;var I=$("0%",1),M=$("50%",1),B=$("100%",1),k={top:I,center:M,bottom:B},W={left:I,center:M,right:B};if(n=n.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=$(e,1)/(/px|rpx$/.test(e)?Y(t.x)?z:S:1);return Y(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return Y(W[e])&&!Y(t.x)?Object.assign(t,{x:W[e]}):Object.assign(t,{y:k[e]||.5})}),{}),(c||u)&&r.translate(c,u),(a||d)&&r.scale(a,d),g){var P=w+S*n.x,O=m+z*n.y;r.translate(P,O),r.rotate(g*Math.PI/180),r.translate(-P,-O)}(y||b)&&r.transform(1,Math.tan(b*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0)},t.prototype.setBackground=function(t,e,i,n,o){var s=this.ctx;t&&t!=L?N(t)?_(t,e,i,n,o,s):s.setFillStyle(t):[r.MP_TOUTIAO,r.MP_BAIDU].includes(j)?s.setFillStyle("rgba(0,0,0,0)"):s.setFillStyle(L)},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var r=i[0],o=i[1],s=i[2],a=i[3];n.setShadow(r,o,s,a)}},t.prototype.setBorder=function(t,e){var i=this.ctx,n=t.width,r=t.height,o=t.left,s=t.top,a=e.border,h=e.borderBottom,d=e.borderTop,l=e.borderRight,c=e.borderLeft,f=e.borderRadius,u=e.lineCap,p=a||{},g=p.borderWidth,v=void 0===g?0:g,y=p.borderStyle,x=p.borderColor,b=h||{},w=b.borderBottomWidth,m=void 0===w?v:w,S=b.borderBottomStyle,z=void 0===S?y:S,I=b.borderBottomColor,M=void 0===I?x:I,B=d||{},k=B.borderTopWidth,W=void 0===k?v:k,P=B.borderTopStyle,O=void 0===P?y:P,T=B.borderTopColor,L=void 0===T?x:T,R=l||{},F=R.borderRightWidth,A=void 0===F?v:F,E=R.borderRightStyle,C=void 0===E?y:E,H=R.borderRightColor,D=void 0===H?x:H,Y=c||{},$=Y.borderLeftWidth,U=void 0===$?v:$,N=Y.borderLeftStyle,_=void 0===N?y:N,X=Y.borderLeftColor,q=void 0===X?x:X,G=f||{},V=G.borderTopLeftRadius,J=void 0===V?f||0:V,Q=G.borderTopRightRadius,Z=void 0===Q?f||0:Q,K=G.borderBottomRightRadius,tt=void 0===K?f||0:K,et=G.borderBottomLeftRadius,it=void 0===et?f||0:et;if(h||c||d||l||a){var nt=function(t,e,n){"dashed"==e?/mp/.test(j)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(n)},rt=function(t,e,n,r,o,s,a,h,d,l,c,f,p,g,v){i.save(),i.setLineCap(v?"square":u),i.setLineWidth(f),nt(f,p,g),i.beginPath(),i.arc(t,e,a,Math.PI*d,Math.PI*l),i.lineTo(n,r),i.arc(o,s,h,Math.PI*l,Math.PI*c),i.stroke(),i.restore()};if(i.save(),a&&!h&&!c&&!d&&!l)return i.setLineWidth(v),nt(v,y,x),this.roundRect(o,s,n,r,f,!1,!!x),void i.restore();m&&rt(o+n-tt,s+r-tt,o+it,s+r,o+it,s+r-it,tt,it,.25,.5,.75,m,z,M,U&&A),U&&rt(o+it,s+r-it,o,s+J,o+J,s+J,it,J,.75,1,1.25,U,_,q,W&&m),W&&rt(o+J,s+J,o+n-Z,s,o+n-Z,s+Z,J,Z,1.25,1.5,1.75,W,O,L,U&&A),A&&rt(o+n-Z,s+Z,o+n,s+r-tt,o+n-tt,s+r-tt,Z,tt,1.75,2,.25,A,C,D,W&&m)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,r){return i(this,void 0,void 0,(function(){var i=this;return n(this,(function(n){return[2,new Promise((function(n,o){i.drawView(e,r,!0,!1,!0);var s=i,a=s.ctx;s.canvas;var h=e.width,d=e.height,l=e.left,c=e.top,f=r||{},u=f.borderRadius,p=void 0===u?0:u,g=f.backgroundImage,v=f.backgroundRepeat,y=void 0===v?"repeat":v;g&&function(t){var o=a.createPattern(t.src,y);a.setFillStyle(o),i.roundRect(l,c,h,d,p,!0,!1),i.setBorder(e,r),n()}(t)}))]}))}))},t.prototype.drawView=function(t,e,i,n,r){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=this.ctx,s=t.width,a=t.height,h=t.left,d=t.top,l=e||{},c=l.borderRadius,f=void 0===c?0:c,u=l.backgroundColor,p=void 0===u?L:u,g=l.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),r&&(o.save(),this.setShadow(e)),i&&this.setBackground(p,s,a,h,d),e.clipPath?this.clipPath(h,d,s,a,e.clipPath,i,!1):this.roundRect(h,d,s,a,f,i,!1),r&&o.restore(),n&&this.setBorder(t,e),"hidden"==g&&o.clip()},t.prototype.drawImage=function(t,e,o,s){return void 0===e&&(e={}),void 0===o&&(o={}),void 0===s&&(s=!0),i(this,void 0,void 0,(function(){var a=this;return n(this,(function(h){switch(h.label){case 0:return[4,new Promise((function(h,d){return i(a,void 0,void 0,(function(){var i,a,d,l,c,f,u,p,g,v,y,x,b,w,m,S,z,I,M,B,k=this;return n(this,(function(n){return i=this.ctx,a=o.borderRadius,d=void 0===a?0:a,l=o.backgroundColor,c=void 0===l?L:l,f=o.objectFit,u=void 0===f?R:f,p=o.backgroundSize,g=void 0===p?R:p,v=o.objectPosition,y=o.backgroundPosition,x=o.boxShadow,o.backgroundImage&&(u=g,v=y),x&&this.drawView(e,Object.assign(o,{backgroundColor:c||x&&(c||"#ffffff")}),!0,!1,!0),b=e.width,w=e.height,m=e.left,S=e.top,i.save(),z=e.contentSize.left-e.borderSize.left,I=e.contentSize.top-e.borderSize.top,s||(this.setOpacity(o),this.setTransform(e,o),this.setBackground(c,b,w,m,S),this.roundRect(m,S,b,w,d,!!(d||!x&&c),!1)),m+=z,S+=I,i.clip(),M=function(t){if(u!==R){var n=function(t,e,i){var n=t.objectFit,r=t.objectPosition,o=e.width/e.height,s=i.width/i.height,a=1,h="contain",d="cover";n==h&&o>=s||n==d&&o<s?a=e.height/i.height:(n==h&&o<s||n==d&&o>=s)&&(a=e.width/i.width);var l=i.width*a,c=i.height*a,f=r||[],u=f[0],p=f[1],g=Y(u)?$(u,e.width):(e.width-l)*(U(u)?$(u,1):{left:0,center:.5,right:1}[u||"center"]),v=Y(p)?$(p,e.height):(e.height-c)*(U(p)?$(p,1):{top:0,center:.5,bottom:1}[p||"center"]),y=function(t,e){return[(t-g)/a,(e-v)/a]},x=y(0,0),b=x[0],w=x[1],m=y(e.width,e.height),S=m[0],z=m[1],I=Math.max,M=Math.min;return{sx:I(b,0),sy:I(w,0),sw:M(S-b,i.width),sh:M(z-w,i.height),dx:I(g,0),dy:I(v,0),dw:M(l,e.width),dh:M(c,e.height)}}({objectFit:u,objectPosition:v},e.contentSize,t),o=n.sx,s=n.sy,a=n.sh,h=n.sw,d=n.dx,l=n.dy,c=n.dh,f=n.dw;j==r.MP_BAIDU?i.drawImage(t.src,d+m,l+S,f,c,o,s,h,a):i.drawImage(t.src,o,s,h,a,d+m,l+S,f,c)}else i.drawImage(t.src,m,S,b,w)},B=function(){i.restore(),k.drawView(e,o,!1,!0,!1),h(1)},function(t){M(t),B()}(t),[2]}))}))}))];case 1:return h.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,n){var r=this,o=this.ctx,s=e.borderSize,a=e.contentSize,h=e.left,d=e.top,l=a.width,c=a.height,f=a.left-s.left||0,u=a.top-s.top||0,p=i.color,g=i.lineHeight,v=i.fontSize,y=i.fontWeight,x=i.fontFamily,b=i.fontStyle,w=i.textIndent,m=void 0===w?0:w,S=i.textAlign,z=i.textStroke,I=i.verticalAlign,M=void 0===I?Ct:I,B=i.backgroundColor,k=i.lineClamp,W=i.backgroundClip,P=i.textShadow,O=i.textDecoration;if(m=Y(m)?m:0,this.drawView(e,i,W!=Rt),g=$(g,v),t){o.save(),h+=f,d+=u;var T=n.fontHeight,L=n.descent,R=void 0===L?0:L,F=n.ascent,A=R+(void 0===F?0:F);switch(o.setFonts({fontFamily:x,fontSize:v,fontWeight:y,fontStyle:b}),o.setTextBaseline(Ct),o.setTextAlign(S),W?this.setBackground(B,l,c,h,d):o.setFillStyle(p),S){case Ht:break;case Dt:h+=.5*l;break;case Yt:h+=l}var E=n.lines*g,C=Math.ceil((c-E)/2);switch(C<0&&(C=0),M){case Et:break;case Ct:d+=C;break;case jt:d+=2*C}var j=(g-T)/2,H=g/2,D=function(t){var e=o.measureText(t),i=e.actualBoundingBoxDescent,n=void 0===i?0:i,r=e.actualBoundingBoxAscent;return M==Et?{fix:A?void 0===r?0:r:H-j/2,lineY:A?0:j-j/2}:M==Ct?{fix:A?H+n/4:H,lineY:A?0:j}:M==jt?{fix:A?g-n:H+j/2,lineY:A?2*j:j+j/2}:{fix:0,height:0,lineY:0}},U=function(t,e,i){var r=t;switch(S){case Ht:t=t,r+=i;break;case Dt:r=(t-=i/2)+i;break;case Yt:r=t,t-=i}if(O){o.setLineWidth(v/13),o.beginPath();var s=.1*n.fontHeight;/\bunderline\b/.test(O)&&(o.moveTo(t,e+n.fontHeight+s),o.lineTo(r,e+n.fontHeight+s)),/\boverline\b/.test(O)&&(o.moveTo(t,e-s),o.lineTo(r,e-s)),/\bline-through\b/.test(O)&&(o.moveTo(t,e+.5*n.fontHeight),o.lineTo(r,e+.5*n.fontHeight)),o.closePath(),o.setStrokeStyle(p),o.stroke()}},N=function(t,e,i){var n=function(){o.setLineWidth(z.width),o.setStrokeStyle(z.color),o.strokeText(t,e,i)},s="outset";z&&z.type!==s?(o.save(),r.setShadow({boxShadow:P}),o.fillText(t,e,i),o.restore(),n()):z&&z.type==s?(o.save(),r.setShadow({boxShadow:P}),n(),o.restore(),o.save(),o.fillText(t,e,i),o.restore()):(r.setShadow({boxShadow:P}),o.fillText(t,e,i))};if(!n.widths||1==n.widths.length&&n.widths[0].total+m<=a.width){var _=D(t),X=_.fix,q=void 0===X?0:X,G=_.lineY;return N(t,h+m,d+q),U(h+m,d+G,n&&n.widths&&n.widths[0].total||n.text),d+=g,o.restore(),void this.setBorder(e,i)}for(var V=d,J=h,Q="",Z=0,K=o.measureText("...").width,tt=n.widths,et=0;et<tt.length;et++){var it=tt[et].widths,nt=0;Q="",d+=1==(Z+=1)?0:g,1==Z&&m&&(nt=m,J=h+m);for(var rt=0;rt<it.length;rt++){1!==Z&&m&&(J=h);var ot=it[rt],st=ot.width,at=ot.text,ht=(it[rt+1]||{}).width;if(Q+=at,(nt+=st)+(void 0===ht?0:ht)+(0==Z?m:0)+(Z==k?K:0)>a.width){Z>=k&&(Q+="…"),Z++,nt=0;var dt=D(Q);q=dt.fix,G=dt.lineY;N(Q,J,d+q),U(J,d+G,nt),d+=g,Q=""}else if(rt==it.length-1){et!=tt.length-1&&Z==k&&K+nt<a.width&&(Q+="…");var lt=D(Q);q=lt.fix,G=lt.lineY;N(Q,J,d+q),U(J,d+G,nt)}if(d>V+c||Z>k)break}}o.restore()}},t.prototype.source=function(t){return i(this,void 0,void 0,(function(){var e,i,r,o,s=this;return n(this,(function(n){switch(n.label){case 0:if(this.node=null,e=+new Date,"{}"==JSON.stringify(t))return[2];if(!t.type)for(i in t.type=At,t.styles=t.styles||t.css||{},t)["views","children","type","css","styles"].includes(i)||(t.styles[i]=t[i],delete t[i]);return t.styles.boxSizing||(t.styles.boxSizing="border-box"),[4,this.create(t)];case 1:return(r=n.sent())?(o=r.layout()||{},this.size=o,this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),this.performance&&console.log("布局用时："+(+new Date-e)+"ms"),[2,this.size]):[2,console.warn("no node")]}}))}))},t.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t,this.useCORS)),this.imageBus[t]},t.prototype.create=function(t,r){return i(this,void 0,void 0,(function(){function i(t,n,r){void 0===n&&(n={}),void 0===r&&(r=!0);var o=[];return t.forEach((function(t){var s=t.styles,a=void 0===s?{}:s,h=t.children,d=void 0===h?[]:h,l=t.text,c=void 0===l?"":l,f=t.type,u=void 0===f?"":f,p={};p=e(r?e({},n):{},a);var g={},v={},y={};Object.keys(p).map((function(t){if(t.includes("padding")||t.includes("margin")){var e=lt(t,p[t]);Object.keys(e).map((function(t){t.includes("Left")?v[t]=e[t]:t.includes("Right")?y[t]=e[t]:g[t]=e[t]}))}}));if(p.textIndent&&(v.textIndent=p.textIndent,delete n.textIndent),""!==c){var x=Array.from(c);x.forEach((function(t,e){var i=Object.assign({},p,g);0===e?Object.assign(i,v):e==x.length-1&&Object.assign(i,y),delete i.padding,delete i.margin,o.push({type:"text",text:t,styles:i})}))}if(u==Lt||u==Ft)o.push(t);else if("block"===a.display&&d.length>0){var b=i(d,p,!1);t.children=b,t.flattened=!0,o.push(t)}else if(d.length>0){b=i(d,p,r);o=o.concat(b)}})),o}var o,s,a,h,d,l,c,f,u,p,g,v,y,x,b,w,m,S,z,I,M;return n(this,(function(n){switch(n.label){case 0:if(!t)return[2];if(t.styles||(t.styles=t.css||{}),o=t.type,s=o==Lt,a=[Rt,Ft].includes(o),h="textBox"==o,d=t.styles||{},l=d.backgroundImage,c=d.display,s&&!t.src&&!t.url)return[2];if(c==W)return[2];if(a||h){if(f=t.children,!t.text&&(!f||f&&!f.length))return[2];f&&f.length&&!t.flattened&&(u=i(t.children),t.type="view",t.children=u)}if(!(s||t.type==At&&l))return[3,4];p=s?t.src:"",g=/url\((.+)\)/.exec(l),l&&g&&g[1]&&(p=g[1]||""),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.getImageInfo(p)];case 2:return v=n.sent(),y=v.width,x=v.height,!(b=v.path)&&s?[2]:(b&&(t.attributes=Object.assign(t.attributes||{},{width:y,height:x,path:b,src:b,naturalSrc:p})),[3,4]);case 3:return w=n.sent(),t.type!=At?[2]:(this.lifecycle("onEffectFail",e(e({},w),{src:p})),[3,4]);case 4:if(this.count+=1,m=new Ot(t,r,this.root,this.ctx),!(S=t.views||t.children))return[3,8];z=0,n.label=5;case 5:return z<S.length?(I=S[z],[4,this.create(I,m)]):[3,8];case 6:(M=n.sent())&&m.add(M),n.label=7;case 7:return z++,[3,5];case 8:return[2,m]}}))}))},t.prototype.drawNode=function(t,e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var i,r,o,s,a,h,d,l,c,f,u,p,g,v,y,x,b,w,m,S,z,I,M;return n(this,(function(n){switch(n.label){case 0:return i=t.layoutBox,r=t.computedStyle,o=t.attributes,s=t.name,a=t.children,h=t.fixedLine,d=t.attributes,l=d.src,c=d.text,f=r.position,u=r.backgroundImage,p=r.backgroundRepeat,["fixed"].includes(f)&&!e?[2]:(this.ctx.save(),s!==At?[3,7]:l&&u?p?[4,this.drawPattern(o,i,r)]:[3,2]:[3,5]);case 1:return n.sent(),[3,4];case 2:return[4,this.drawImage(o,i,r,!1)];case 3:n.sent(),n.label=4;case 4:return[3,6];case 5:this.drawView(i,r),n.label=6;case 6:return[3,10];case 7:return s===Lt&&l?[4,this.drawImage(o,i,r,!1)]:[3,9];case 8:return n.sent(),[3,10];case 9:s===Rt?this.drawText(c,i,r,o):s===Ft&&Tt.api&&Tt.api.draw(c,this,i,r),n.label=10;case 10:if(this.progress+=1,v=(g=h||{}).beforeElements,y=g.afterElements,!v)return[3,14];x=0,b=v,n.label=11;case 11:return x<b.length?(M=b[x],[4,this.drawNode(M)]):[3,14];case 12:n.sent(),n.label=13;case 13:return x++,[3,11];case 14:if(!a)return[3,18];w=Object.values?Object.values(a):Object.keys(a).map((function(t){return a[t]})),m=0,S=w,n.label=15;case 15:return m<S.length?"absolute"===(M=S[m]).computedStyle.position?[3,17]:[4,this.drawNode(M)]:[3,18];case 16:n.sent(),n.label=17;case 17:return m++,[3,15];case 18:if(!y)return[3,22];z=0,I=y,n.label=19;case 19:return z<I.length?(M=I[z],[4,this.drawNode(M)]):[3,22];case 20:n.sent(),n.label=21;case 21:return z++,[3,19];case 22:return this.ctx.restore(),[2]}}))}))},t.prototype.render=function(t){var e=this;return void 0===t&&(t=30),new Promise((function(r,o){return i(e,void 0,void 0,(function(){var e,i,s,a,h,d,l,c,f,u;return n(this,(function(n){switch(n.label){case 0:return e=+new Date,this.init(),[4,(p=t,void 0===p&&(p=0),new Promise((function(t){return setTimeout(t,p)})))];case 1:n.sent(),n.label=2;case 2:if(n.trys.push([2,14,,15]),!this.node)return[3,12];if(i=this.root.fixedLine||{},s=i.beforeElements,a=i.afterElements,!s)return[3,6];h=0,d=s,n.label=3;case 3:return h<d.length?(f=d[h],[4,this.drawNode(f,!0)]):[3,6];case 4:n.sent(),n.label=5;case 5:return h++,[3,3];case 6:return[4,this.drawNode(this.node)];case 7:if(n.sent(),!a)return[3,11];l=0,c=a,n.label=8;case 8:return l<c.length?(f=c[l],[4,this.drawNode(f,!0)]):[3,11];case 9:n.sent(),n.label=10;case 10:return l++,[3,8];case 11:return r(this.node),[3,13];case 12:this.lifecycle("onEffectFail","node is empty"),n.label=13;case 13:return[3,15];case 14:return u=n.sent(),this.lifecycle("onEffectFail",u),o(u),[3,15];case 15:return this.performance&&console.log("渲染用时："+(+new Date-e-30)+"ms"),[2]}var p}))}))}))},t.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},t.prototype.destroy=function(){this.node=[]},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,r=e.quality,o=void 0===r?1:r;return this.canvas.toDataURL("image/"+n,o)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},t}();r.WEB==j&&(window.Painter=$t),t.Painter=$t,t.default=$t,Object.defineProperty(t,"__esModule",{value:!0})}));
