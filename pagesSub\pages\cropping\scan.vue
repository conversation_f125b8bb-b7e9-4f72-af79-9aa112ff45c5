<template>
	<view class="page-container">
		<!-- 基本信息卡片 -->
		<view class="info-card">
			<view class="card-header">
				<text class="header-title">基本信息</text>
			</view>
			<view class="info-list">
				<view class="info-item">
					<text class="info-label">款式</text>
					<text class="info-value">{{pStyle}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">颜色</text>
					<text class="info-value">{{pColor}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">包号</text>
					<text class="info-value">{{pPackgNum}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">尺码</text>
					<text class="info-value">{{pSize}}</text>
				</view>
				<view class="info-item">
					<text class="info-label">件数</text>
					<input class="info-input" v-model="pQuantity" type="number" placeholder="请输入件数" @blur="blurEvent">
					<text class="tips-label">(件数可修改)</text>
				</view>
			</view>
		</view>

<!-- 工序列表卡片 -->
<view class="process-card">
    <view class="card-header">
        <text class="header-title">工序列表</text>
        <text class="header-action" @click="toggleOtherProcesses" v-if="otherProcesses.length > 0">
            {{ showOtherProcesses ? '收起其他工序' : `展开其他工序(${otherProcesses.length})` }}
        </text>
        <text class="header-action" @click="croppingEvent">查看裁剪单</text>
    </view>
    <view class="process-list">
        <!-- 显示缓存匹配的工序（常用工序） -->
        <view class="process-item" v-for="item in matchedProcesses" :key="item.ID">
            <view class="process-info">
                <text class="process-name">{{item.process?.name}}</text>
                <text class="process-tag cached">常用</text>
            </view>
            <view class="process-action">
                <button class="receive-btn" @click="handleReceive(item)" 
                    v-if="item.leaveNum > 0" 
                    :class="{ disabled: isSubmitting }">
                    {{isSubmitting ? '处理中...' : '领取'}}
                </button>
                <text class="completed-text" v-else>已领完</text>
            </view>
        </view>
        
        <!-- 显示其他工序（可折叠） -->
        <view v-if="showOtherProcesses || otherProcesses.length === 0">
            <view class="process-item" v-for="item in otherProcesses" :key="item.ID">
                <view class="process-info">
                    <text class="process-name">{{item.process?.name}}</text>
                </view>
                <view class="process-action">
                    <button class="receive-btn" @click="handleReceive(item)" 
                        v-if="item.leaveNum > 0" 
                        :class="{ disabled: isSubmitting }">
                        {{isSubmitting ? '处理中...' : '领取'}}
                    </button>
                    <text class="completed-text" v-else>已领完</text>
                </view>
            </view>
        </view>
    </view>
</view>

		<!-- 操作选项卡片 -->
		<view class="option-card">
			<view class="checkbox-item" @click="cbxClick()">
				<checkbox class="custom-checkbox">下次按此工序领取</checkbox>
			</view>
			<view class="cropping-link" @click="croppingEvent">
				<text class="link-text">此款裁剪单</text>
			</view>
		</view>

		<!-- 底部按钮区 -->
		<view class="bottom-actions">
			<!-- <button class="primary-btn" v-if="overFlag" @click="rcvEvent">全部领取</button> -->
			<!--<button class="disabled-btn" v-else>已领完</button>-->
			<button class="secondary-btn" @click="cancelEvent">取消</button>
		</view>
	</view>

	<issue></issue>
	
	<!-- 打印弹窗 -->
	<popup :show="isShow" @close="handleToggle" style="z-index: 999999;">
		<view class="print-modal">
			<!-- 下滑指示器 -->
			<view class="swipe-indicator">
				<view class="swipe-bar"></view>
			</view>
			<view class="modal-header">
				<text class="modal-title">当前打印内容</text>
			</view>
			<view class="modal-content">
				<empty v-if="qrStyle==''"></empty>
				<view class="print-info" v-else>
					<view class="print-row">
						<text class="print-label">款式：</text>
						<text class="print-value">{{qrStyle && qrStyle.includes('/') ? qrStyle.split('/')[0] : qrStyle}}</text>
					</view>
					<view class="print-row" v-if="qrStyle && qrStyle.includes('/')">
						<text class="print-label">订单：</text>
						<text class="print-value">{{qrStyle.split('/')[1] || ''}}</text>
					</view>
					<view class="print-row">
						<text class="print-label">包号：</text>
						<text class="print-value">{{qrNumber}}</text>
					</view>
					<view class="print-row">
						<text class="print-label">颜色：</text>
						<text class="print-value">{{qrColor}}</text>
					</view>
					<view class="print-row">
						<text class="print-label">尺寸：</text>
						<text class="print-value">{{qrSize}}</text>
					</view>
					<view class="print-row">
						<text class="print-label">件数：</text>
						<text class="print-value">{{qrQuantity}}</text>
					</view>
					<view class="qr-container">
						<canvas id="qrcode" canvas-id="qrcode" class="qrcode"></canvas>
					</view>
					<view class="modal-actions">
						<button class="cancel-print-btn" @click="cancelEventTeam">取消打印</button>
					</view>
				</view>
			</view>
		</view>
	</popup>
</template>

<script setup>
import { ref, reactive, computed, toRaw } from 'vue' 
import { onLoad, onShow } from "@dcloudio/uni-app"
import Https from '@/apis/index.js'
import Enum from '@/utils/enum.js'
import { isEmpty } from '@/utils/index.js'
import userInfoStore from '@/store/userInfo.js'
import { storeToRefs } from 'pinia'
import tsc from '@/components/gprint/tsc.js' 
import UQRCode from '@/uni_modules/Sansnn-uQRCode/js_sdk/uqrcode/uqrcode.js'
import { TextEncoder, TextDecoder } from 'text-decoding'
let idx
let styleName
const store = userInfoStore()
const { roleID, teamID, user,companyID } = storeToRefs(store)
// 缓存相关变量 - 在这里
const CACHE_KEY = 'user_selected_process_cache'
const cachedProcessNames = ref([])
const matchedProcesses = ref([])
const otherProcesses = ref([])
const showOtherProcesses = ref(false)

let isShow = ref(false)
const isShowAll = ref(false)
const isDaiXu = ref(false)
const info = ref({})
const sizeDetail = ref([])
const Ulist = ref([{ name: '', count: 0, selectIndex: 0 }])
const typeIndex = ref(0)
let currPickerIndex=ref(0)
let pCroppingID=ref(0)
let pColor=ref("")
let pPackgNum=ref("")
let pSize=ref("")
let pSizeID=ref("")
let pStyleID=ref("")
let pStyle=ref(0)
let pQuantity=ref(0)
let pQuantityPre=ref(0)
let userSelectList = []
let userSelectIndex = ref(null)
let currPrinter = ref("设备未连接")
let qrcodeHeight=380;
let qrcodeWith=380;
let qrcodeSize=220;
let qrQuantity=ref(0)
let qrStyle=ref('')
let qrNumber=ref(0)
let qrColor=ref('')
let qrSize=ref(0)
let overFlag=ref(false);
let timer;
// 缓存管理方法 - 在这里
const loadProcessCache = () => {
    try {
        const cached = uni.getStorageSync(CACHE_KEY)
        if (cached) {
            cachedProcessNames.value = JSON.parse(cached)
            console.log('加载工序缓存:', cachedProcessNames.value)
        }
    } catch (error) {
        console.error('加载工序缓存失败:', error)
        cachedProcessNames.value = []
    }
}

const saveProcessToCache = (processName) => {
    try {
        // 直接覆盖缓存，而不是追加
        cachedProcessNames.value = [processName]
        uni.setStorageSync(CACHE_KEY, JSON.stringify(cachedProcessNames.value))
        console.log('覆盖工序缓存:', processName, cachedProcessNames.value)
    } catch (error) {
        console.error('保存工序缓存失败:', error)
    }
}

const categorizeProcesses = (processes) => {
    const matched = []
    const others = []
    
    processes.forEach(process => {
        if (cachedProcessNames.value.includes(process.process?.name)) {
            matched.push(process)
        } else {
            others.push(process)
        }
    })
    
    matchedProcesses.value = matched
    otherProcesses.value = others
    
    console.log('工序分类结果:', {
        matched: matched.length,
        others: others.length,
        cachedNames: cachedProcessNames.value
    })
}

const toggleOtherProcesses = () => {
    showOtherProcesses.value = !showOtherProcesses.value
}

const onInputEvent=(obj)=>{
	console.log("=====")
	console.log(obj)
	obj.detail.value = "测试"
	console.log(obj)
	
}
const GenQRCode=(data)=> {

	 // console.log(selectedGroup)
	 // console.log(selectedRole)
  // 获取uQRCode实例
  var qr = new UQRCode();
  // 设置二维码内容
  qr.data = "https://www.baidu.com/";
  // console.log(arrayRole[indexRole])
  if(qrStyle.value!=""){
	  qr.data=data;//qrStyle.value+";"+qrNumber.value+";"+qrColor.value+";"+qrSize.value+";"+qrQuantity.value
  }
   // console.log(qr.data)
  // 设置二维码大小，必须与canvas设置的宽高一致
  qr.size = qrcodeSize;
  // 调用制作二维码方法
  qr.make();
  // 获取canvas上下文
  var canvasContext = uni.createCanvasContext('qrcode', this); // 如果是组件，this必须传入
  // console.log(canvasContext)
  // 设置uQRCode实例的canvas上下文
  qr.canvasContext = canvasContext;
  // 调用绘制方法将二维码图案绘制到canvas上
  qr.drawCanvas();
  
}
const pickClickEvent = (pickerIndex) => {
	currPickerIndex = pickerIndex;
	console.log(currPickerIndex)
}
const handleSelectUser = ({detail}) => {
	console.log(userSelectList)
	userSelectIndex.value = Number(detail.value)
	// console.log(Ulist.value[currPickerIndex])
	Ulist.value[currPickerIndex].selectIndex=Number(detail.value)
	// console.log(userSelectList[Number(detail.value)])
	Ulist.value[currPickerIndex].name=userSelectList[Number(detail.value)].username
	Ulist.value[currPickerIndex].uid=userSelectList[Number(detail.value)].Id
	
	getUserList()
}
const connetEvent=()=>{
	uni.navigateTo({
		url:'/pagesSub/pages/print/printset?id='+idx+'&name='+styleName
	})
}
const resetEvent=()=>{
	pSize.value=""
	pPackgNum.value=""
	pColor.value=""
	_search()
}
const searchEvent=()=>{
	_search()
}
const cbxClick=(e)=>{
	 // console.log(e)
	 // hasSettleData.value=false;
	list.value.forEach(function(item,index){
		if(item.sizeID==e)
		{
			item.ischecked=!item.ischecked
		}
	});
	// list.value.forEach(function(item,index){
	// 	if(item.settleFlag==1&&item.ischecked){
	// 		hasSettleData.value=true;
	// 	}
	// });
	// console.log(list.value)
}
const cbxClickAll=(e)=>{
	// console.log("all")
	// hasSettleData.value=false;
	list.value.forEach(function(item,index){
		item.ischecked=e
	});
	// console.log(list.value)
	// list.value.forEach(function(item,index){
	// 	console.log(item.ischecked)
	// 	if(item.settleFlag==1&&item.ischecked){
	// 		hasSettleData.value=true;
	// 	}
	// });
}
const handleDaiXuSubmit = async({detail}) => {
	// 防重复提交检查
	if (isSubmitting.value) {
		uni.showToast({
			icon: 'none',
			title: '请勿重复提交，正在处理中...',
			duration: 2000
		});
		return;
	}

	console.log(teamID.value)
	let flag =false;
	const formData = detail.value
	Ulist.value.forEach(function(itemTemp,index){
		let item = toRaw(itemTemp);
		if (isEmpty(item.name)) {
			uni.showToast({
				title: '请选择领取人',
				icon: 'none'
			})
			flag=true;
			return
		}
		if ((item.count === undefined||Number(item.count)<=0)&&!isShowAll.value) {
			uni.showToast({
				title: '请输入领取件数',
				icon: 'none'
			})
			flag=true;
			return
		}
	});
	
	if(!flag){
		// 设置提交状态为正在提交
		isSubmitting.value = true;
		
		try {
			//代序所有
			if(isShowAll.value)
			{
				let formDataDXAll = {
					companyId: companyID.value.toString(),
					croppingId: info.value.ID.toString(),
					teamId: teamID.value.toString(),
					userId: Ulist.value[0].uid.toString(),
					processId: receiveForm.processID.toString(),
					size: receiveForm.size,
					quantity: receiveForm.quantity.toString(),
					jobType:"1"
				}
				// console.log(formDataDXAll)
				let replyRAll = await Https.Job.AddJobAll(formDataDXAll)
				
				// console.log(replyRAll)
				uni.showToast({
					icon: 'none',
					mask: true,
					title: (replyRAll.Success?"领取成功":("领取失败("+replyRAll.ErrMsg+")")),
					duration:2000
				});
				isDaiXu.value = false
				if(replyRAll.Success){
                    // 保存工序名称到缓存
                    saveProcessToCache(item.process.name)

					setTimeout(function(){
						 pQuantity.value = pQuantityPre.value;
                         uni.navigateTo({
                         url: '/pagesSub/pages/cropping/scan?id=' + pCroppingID.value + '&styleid=' + pStyleID.value + '&sizeid=' + pSizeID.value + '&name=' + 
						 pStyle.value + '&pknum=' + pPackgNum.value + '&size=' + pSize.value + '&num=' + pQuantity.value + '&color=' + pColor.value
                        }) 
					}, 500)
				}
			}
			else{
				//代序单个尺码 
				let userIdList = [];
				let qantityList = [];
				Ulist.value.forEach(function(item,index){
					userIdList.push(item.uid)
					qantityList.push(item.count)
				});
				// console.log(receiveForm)
				let formDataDX = {
					companyId: companyID.value.toString(),
					croppingId: info.value.ID.toString(),
					teamId: teamID.value.toString(),
					userId: userIdList.join(','),
					processId: receiveForm.processID.toString(),
					size: receiveForm.size,
					quantity: qantityList.join(','),
					jobType:"1"
				}
				// console.log(formDataDX)
				let replyR = await Https.Job.AddJobForOther(formDataDX)
				
				// console.log(replyR)
				uni.showToast({
					icon: 'none',
					mask: true,
					title: (replyR.Success?"领取成功":("领取失败("+replyR.ErrMsg+")")),
					duration:2000
				});
				// getUser()
				isReceive.value = false
				isDaiXu.value = false
				if(replyR.Success){
					setTimeout(function(){
						// isShow.value=false
						// console.log(info.value)
						// console.log(info.value.sizeName)
						loadJobData(info.value, info.value.sizeName) 
						//getProcess()
					}, 500)
				}
				else{
					// setTimeout(function(){
					// 	// isShow.value=false
					// 	getProcess()
					// }, 1500)
				}
			}
		} catch (error) {
			console.error('领取失败:', error);
			uni.showToast({
				icon: 'none',
				title: '网络错误，请重试',
				duration: 2000
			});
		} finally {
			// 重置提交状态
			isSubmitting.value = false;
		}
	}
}
const handleAdd = () => {
	Ulist.value.push({ name: '', price: 0, selectIndex: 0 })
}
const handleDel = (index) => { 
	Ulist.value.splice(index, 1)
}
const handleDaiXuSave = (index) => {
	Ulist.value.splice(index, 1)
}
const editEvent = ()=>{
	uni.navigateTo({
		url: '/pagesSub/pages/cropping/edit?id=' + info.value.ID
	})
}
const handleToggle = async(item, size) => {
	// console.log(item)
	// console.log(size)
	
	await loadJobData(item, size) 
	
	isShow.value = !isShow.value
}
const handleToggleAll = async(item, size) => {
	// console.log(item)
	// console.log(size)
	
	await loadJobData(item, size) 
	
	isShowAll.value = !isShowAll.value;
}
const loadJobData=async(item, size)=>{  
	 console.log(item)
	// console.log(size)
	let sizePatam=size
	if(size==="全部")sizePatam="";
	if (item) {
		info.value = {
			...item,
			sizeName: size
		}
		let sizeList = toRaw(item.SizeList);
		// console.log(sizeList)
		let quantity =0;
		if(size==="全部"){
			sizeList.forEach(function(itemSize,indexSize){
				quantity += itemSize.quantity
			});
		}
		else
		{
			sizeList.forEach(function(itemSize,indexSize){
				if(itemSize.size==size)
				{
					quantity = itemSize.quantity
				}
			});
		}
		let proccessId=item.ID.toString();
		if(item.ID.toString().indexOf('|')>-1){
			let arr = item.ID.toString().split('|')
			proccessId=arr[1];
		}
		// console.log(proccessId)
		// console.log(sizePatam)
		// 改为使用款式ID获取工序数据，确保工序与款式关联
		let data = [];
		
		console.log('=== 扫描页面工序获取开始 ===');
		console.log('当前款式ID (idx):', idx);
		
		if (idx != undefined && idx !== null) {
			// 根据款式ID获取工序列表
			console.log('调用Https.Process.ListNew API，参数:', { styleID: idx });
			
			let replyR = await Https.Process.ListNew({
				styleID: idx
			})
			
			console.log('API返回结果:', replyR);
			
			if (replyR.Success && replyR.ResData && replyR.ResData.list && replyR.ResData.list.length > 0) {
				// 将工序数据转换为与原来格式兼容的结构
				data = replyR.ResData.list.map(processItem => ({
					process: {
						ID: processItem.ID,
						name: processItem.name,
						price: processItem.price
					},
					leaveNum: 0, // 后续会计算
					receveUser: "",
					receveNum: 0,
					jobs: []
				}));
				console.log('✅ 成功获取到款式关联工序，数量:', data.length);
			} else {
				console.log('❌ 未获取到款式关联工序，显示提示信息');
				data = [{
					process: {
						ID: 0,
						name: '该款式暂无关联工序',
						price: 0
					},
					leaveNum: 0,
					receveUser: "",
					receveNum: 0,
					jobs: []
				}];
			}
		} else {
			console.log('❌ 款式ID未定义，无法获取工序');
			data = [{
				process: {
					ID: 0,
					name: '款式ID未定义',
					price: 0
				},
				leaveNum: 0,
				receveUser: "",
				receveNum: 0,
				jobs: []
			}];
		}
		 // console.log(data)
		let processJobs= await Https.Job.JobReceiveListByCroppingRecord({
		 	userId:'',// item.userID.toString(),
			croppingId:item.ID.toString(),
			size:sizePatam,
			processName:''
		 })
		  console.log(processJobs)
		data.forEach(function(itemData,indexData){
			data[indexData].leaveNum=quantity;
			data[indexData].receveUser="";
			data[indexData].receveNum=0;
			processJobs.ResData.forEach(function(item,index){
				if(itemData.process.name==item.process_name){
					data[indexData].jobs=processJobs.ResData.filter(itemTemp => itemTemp.process_name===item.process_name);
					data[indexData].receveUser+="|"+item.username
					data[indexData].receveNum+= parseInt(item.real_quantity)
					data[indexData].leaveNum=data[indexData].leaveNum-parseInt(item.real_quantity)
				}
			})
			if(data[indexData].receveUser.indexOf('|')==0){
				data[indexData].receveUser = data[indexData].receveUser.substring(1);
			}
		})
		 console.log(data)
		sizeDetail.value = data
		
		sizeDetail.value.forEach(function(item,index){
			if(item.leaveNum>0){
				overFlag.value=true;
				
			}
		})
	}
	categorizeProcesses(sizeDetail.value)
}

const isNav = ref(false)
const handleTap = (item) => {
	return;
	isNav.value = !isNav.value
	item && (info.value = item)
}
const handleDaiXuTap = (item) => {
	isDaiXu.value = !isDaiXu.value
}
const formatSize = (name, item) => {
	let str = 0
	for (let i in item) {
		// 只累加正数，负数不参与统计
		if(item[i].size.toUpperCase() === name && item[i].quantity > 0) {
			str += item[i].quantity
		}
	}
	return str ? str : ''
}
const handleSize = ({detail}, index) => {
	jobs.value[index].sizeIndex = Number(detail.value)
	jobs.value[index].size = Enum.size[Number(detail.value)].value
	checkNum(Enum.size[Number(detail.value)].value, index)
}

const handleCloth = () => {
	uni.navigateTo({
		url: '/pagesSub/pages/cloth/cloth?id=' + idx
	})
}
const handlePagin = (detail) => {
	// page.value = detai.value
	// _search()
}
const croppingEvent=()=>{
	console.log(pCroppingID.value)
	console.log(styleName.value)
	uni.navigateTo({
		url: '/pagesSub/pages/cropping/cropping?id='+pStyleID.value+"&name="+pStyle.value
	})
}
const rcvEvent = async () => {
	// 防重复提交检查
	if (isSubmitting.value) {
		uni.showToast({
			icon: 'none',
			title: '请勿重复提交，正在处理中...',
			duration: 2000
		});
		return;
	}

	let i = 0;
	let flag = true;
	const validItems = sizeDetail.value.filter(item => item.leaveNum > 0);
	
	if (validItems.length === 0) {
		uni.showToast({
			icon: 'none',
			mask: true,
			title: "已领完，请勿重复操作",
			duration: 2000
		});
		return;
	}

	// 设置提交状态为正在提交
	isSubmitting.value = true;
	
	try {
		// 逐个处理，避免并发问题
		for (const item of validItems) {
			try {
				let formData = {
					companyId: companyID.value.toString(),
					croppingId: pCroppingID.value.toString(),
					teamId: teamID.value.toString(),
					userId: user.value.ID.toString(),
					processId: item.process.ID.toString(),
					size: pSize.value.toString(),
					quantity: item.leaveNum.toString(),
					jobType: "1"
				}
				console.log(formData)
				let replyR = await Https.Job.AddJob(formData)
				
				console.log(replyR)
				if (!replyR.Success) {
					flag = false;
					console.error('工序领取失败:', item.process.name, replyR.ErrMsg);
				}
			} catch (error) {
				flag = false;
				console.error('工序领取异常:', item.process.name, error);
			}
			
			// 添加小延时避免服务器压力
			await new Promise(resolve => setTimeout(resolve, 100));
		}

		uni.showToast({
			icon: 'none',
			mask: true,
			title: flag ? "领取成功" : "部分领取失败",
			duration: 2000
		});

		// 延时跳转，确保操作完成
		setTimeout(function() {
			pQuantity.value = pQuantityPre.value;
			uni.navigateTo({
				url: '/pagesSub/pages/cropping/scan?id=' + pCroppingID.value + '&styleid=' + pStyleID.value + '&sizeid=' + pSizeID.value + '&name=' + pStyle.value + '&pknum=' + pPackgNum.value + '&size=' + pSize.value + '&num=' + pQuantity.value + '&color=' + pColor.value
			})
		}, 2500);
		
	} catch (error) {
		console.error('全部领取失败:', error);
		uni.showToast({
			icon: 'none',
			title: '网络错误，请重试',
			duration: 2000
		});
	} finally {
		// 重置提交状态
		isSubmitting.value = false;
	}
}
const blurEvent=()=>{
	if(pQuantity.value>pQuantityPre.value){
		uni.showToast({
			icon: 'none',
			mask: true,
			title: "领取数量不能大于输入件数",
			duration:2000
		});
	}
	if(pQuantity.value<=0){
		uni.showToast({
			icon: 'none',
			mask: true,
			title: "领取数量必须大于0",
			duration:2000
		});
	}
}
const cancelEvent=()=>{
	uni.reLaunch({
	  url: '/pages/index/index'
	})
}

const handleTop = async(id) => {
	const { msg: title } = await Https.Job.ChangeWorker({
		id, userID: user.value.ID
	})
	uni.showToast({
		title
	})
	handleToggle()
}
const isReceive = ref(false)
const receiveForm = reactive({
	croppingID: null,
	processID: null,
	teamID: teamID.value,
	jobType: 1,
	size: '',
	quantity: 0,
	max: 0
})

const handleDaiXu = async(itemTemp) => {
	// console.log(toRaw(item))
	// console.log(info.value)
	Ulist.value.splice(0)
	getUserList()
	if (itemTemp) {
		let item= toRaw(itemTemp)
		const { data } = await Https.Inventory.Stock({   //size_list table
			croppingRecordID: info.value.ID,
			size: info.value.sizeName,
			processID: item.process.ID
		})
		// console.log(data)
		let max = 0
		if (data.list) {
			for (let i in data.list) {
				const item = data.list[i]
				item.margin && (max += item.margin)
			}
		}
		receiveForm.croppingID = info.value.ID
		receiveForm.processID = item.process.ID
		receiveForm.size = info.value.sizeName
		receiveForm.quantity = 0
		receiveForm.max =item.leaveNum// max
		
		Ulist.value.splice(0)
		Ulist.value.push({ name: '', price: 0, selectIndex: 0 })
		
		info.value.process = toRaw(item).process.name
		isDaiXu.value=true
	}
}
const handleDaiXuAll = async(itemTemp) => {
	// console.log(itemTemp)
	Ulist.value.splice(0)
	getUserList()
	// console.log(info.value.ID)
	let proccessId=info.value.ID.toString();
	if(info.value.ID.toString().indexOf('|')>-1){
		let arr = info.value.ID.toString().split('|')
		proccessId=arr[1];
	}
	if (itemTemp) {
		let item= toRaw(itemTemp)
		const { data } = await Https.Inventory.Stock({   //size_list table
			croppingRecordID:proccessId,// info.value.ID,
			size: info.value.sizeName,
			processID: item.process.ID
		})
		console.log(data)
		let max = 0
		if (data.list) {
			for (let i in data.list) {
				const item = data.list[i]
				item.margin && (max += item.margin)
			}
		}
		receiveForm.croppingID = info.value.ID
		receiveForm.processID = item.process.ID
		receiveForm.size = info.value.sizeName
		receiveForm.quantity = 0
		receiveForm.max =item.leaveNum// max
		
		Ulist.value.splice(0)
		Ulist.value.push({ name: '', price: 0, selectIndex: 0 })
		
		info.value.process =toRaw(item).process.name
		isDaiXu.value=true
	}
}
const handleSwapReceive = async(item) => {
	isReceive.value = !isReceive.value
	console.log(item)
	if (item) {
		const { data } = await Https.Inventory.Stock({   //size_list table
			croppingRecordID: info.value.ID,
			size: info.value.sizeName,
			processID: item.process.ID
		})
		console.log(data)
		let max = 0
		if (data.list) {
			for (let i in data.list) {
				const item = data.list[i]
				item.margin && (max += item.margin)
			}
		}
		receiveForm.croppingID = info.value.ID
		receiveForm.processID = item.process.ID
		receiveForm.size = info.value.sizeName
		receiveForm.quantity = 0
		receiveForm.max =item.leaveNum// max
	}
}
const handleSwapReceiveAll = async(item) => {
	// 防重复提交检查
	if (isSubmitting.value) {
		uni.showToast({
			icon: 'none',
			title: '请勿重复提交，正在处理中...',
			duration: 2000
		});
		return;
	}

	console.log(item)
	if (item) {
		// 设置提交状态
		isSubmitting.value = true;
		
		try {
			receiveForm.croppingID = info.value.ID
			receiveForm.processID = item.process.ID
			receiveForm.size = info.value.sizeName
			receiveForm.quantity = 0
			receiveForm.max =item.leaveNum
			
			const formData = {
				companyId: companyID.value.toString(),
				croppingId: info.value.ID.toString(),
				teamId: teamID.value.toString(),
				userId: user.value.ID.toString(),
				processId: receiveForm.processID.toString(),
				size: receiveForm.size,
				quantity: receiveForm.quantity.toString(),
				jobType:"1"
			}
			let replyR = await Https.Job.AddJobAll(formData)
			
			console.log(replyR)
			uni.showToast({
				icon: 'none',
				mask: true,
				title: (replyR.Success?"领取成功":("领取失败("+replyR.ErrMsg+")")),
				duration:2000
			});
			
			if(replyR.Success){
				setTimeout(function(){
					console.log(info.value)
					console.log(info.value.sizeName)
					loadJobData(info.value, info.value.sizeName) 
				}, 500)
			}
		} catch (error) {
			console.error('领取失败:', error);
			uni.showToast({
				icon: 'none',
				title: '网络错误，请重试',
				duration: 2000
			});
		} finally {
			// 重置提交状态
			isSubmitting.value = false;
		}
	}
}
const handleReceive = async(item) => {
	// 防重复提交检查
	if (isSubmitting.value) {
		uni.showToast({
			icon: 'none',
			title: '请勿重复提交，正在处理中...',
			duration: 2000
		});
		return;
	}

	if(parseFloat(pQuantity.value)>parseFloat(pQuantityPre.value)||parseFloat(pQuantity.value)<=0){
		uni.showToast({
			icon: 'none',
			mask: true,
			title: "领取数量不合法",
			duration:2000
		});
		return;
	}

	// 设置提交状态
	isSubmitting.value = true;
	
	try {
		receiveForm.quantity = Number(receiveForm.quantity)
		
		const formData = {
			companyId: companyID.value.toString(),
			croppingId: pCroppingID.value.toString(),
			teamId: teamID.value.toString(),
			userId: user.value.ID.toString(),
			processId:item.process.ID.toString(),
			size: pSize.value.toString(),
			quantity: pQuantity.value.toString(),
			jobType:"1"
		}
		let replyR = await Https.Job.AddJob(formData)
		
		uni.showToast({
			icon: 'none',
			mask: true,
			title: (replyR.Success?"领取成功":("领取失败("+replyR.ErrMsg+")")),
			duration:2000
		});
		if(replyR.Success){
            // 保存工序名称到缓存
            saveProcessToCache(item.process.name)			
            setTimeout(function(){
                pQuantity.value = pQuantityPre.value;
                uni.navigateTo({
                    url: '/pagesSub/pages/cropping/scan?id=' + pCroppingID.value + '&styleid=' + pStyleID.value + '&sizeid=' + pSizeID.value + '&name=' + pStyle.value + '&pknum=' + pPackgNum.value + '&size=' + pSize.value + '&num=' + pQuantity.value + '&color=' + pColor.value
                })
            }, 500)
        }
	} catch (error) {
		console.error('领取失败:', error);
		uni.showToast({
			icon: 'none',
			title: '网络错误，请重试',
			duration: 2000
		});
	} finally {
		// 重置提交状态
		isSubmitting.value = false;
	}
}

const jobs = ref([{
	userIndex: null,
	userId: 0,
	processIndex: null,
	processId: 0,
	sizeIndex: null,
	size: '',
	quantity: 0,
	type: null,
	residue: 0
}])
const checkNum = async(size, index) => {
	if (!size) return
	if (!jobs.value[index].processId) return
	const { data } = await Https.Inventory.Stock({
		size,
		croppingRecordID: info.value.ID,
		processID: jobs.value[index].processId
	})
	if (data.list.length) {
		for (let i in data.list) {
			const item = data.list[i]
			item.margin && (jobs.value[index].residue += item.margin)
		}
	} else {
		jobs.value[index].residue = 0
		jobs.value[index].quantity = 0
	}
}
const handleQuantity = ({detail}, index) => {
	jobs.value[index].quantity = detail.value
}
const sliderChange=(e)=>{
	 // console.log('value 发生变化：' + e.detail.value)
	 receiveForm.quantity=e.detail.value
}
const handleAddTask = () => {
	jobs.value.push({
		userIndex: null,
		userId: 0,
		processIndex: null,
		processId: 0,
		sizeIndex: null,
		size: '',
		quantity: 0,
		type: null
	})
}
const isTask = ref(false)
const handleTask = () => {
	isTask.value = !isTask.value
	isNav.value = false
}
const handleSubmitTask = async() => {
	const formData = {
		croppingId: info.value.ID,
		teamId: teamID.value,
		jobs: jobs.value.map(item => ({
			userId: item.userId,
			processId: item.processId,
			size: item.size,
			quantity: item.quantity
		}))
	}
	console.log(formData)
	const { msg: title } = await Https.Job.Task(formData)
	uni.showToast({ title })
	handleTask()
	_search()
}
const userList = ref([])
const getUser = async() => {
	const { data } = await Https.AppUser.UserList({ teamID: teamID.value })
	userList.value = data.list
}
const handleUser = ({detail}, index) => {
	jobs.value[index].userIndex = Number(detail.value)
	jobs.value[index].userId = userList.value[Number(detail.value)].ID
}
const handleType = ({detail}, index) => {
	jobs.value[index].type = Number(detail.value)
}
const process = ref([])
const getProcess = async() => {
	if(idx==undefined) return;
	let replyR = await Https.Process.ListNew({ styleID: idx }) 
	if(replyR.Success){
		process.value = replyR.ResData.list;
	}
}
const handleProcess = ({detail}, index) => {
	jobs.value[index].processIndex = Number(detail.value)
	jobs.value[index].processId = process.value[Number(detail.value)].ID
	checkNum('', index)
}


const handleDetail = (id) => {
	uni.navigateTo({
		url: '/pagesSub/pages/cropping/detail?id=' + id
	})
}

let title = ref('裁剪单')
let titleBtn = ref('汇总明细')
const quantity = ref(0)
const margin = ref(0)
const list = ref([])
const listTotal = ref([])
const listFinished =ref([])
const sizeArr = ref([])
const _search = async() => {
	const { data } = await Https.CroppingRecord.List({ styleID: idx })
	console.log(data)
	if(data.list.length>0){
		// console.log(data.list[0].style.CreatedAt)
		var d = new Date(data.list[0].style.CreatedAt)
		
		var year = d.getYear() + 1900; // getYear返回的是从1900年开始算起的年份，需加上1900
		var month = d.getMonth() + 1; // getMonth返回的是0-11，需要加1
		var day = d.getDate();
		  
		// 格式化为yyyy-mm-dd格式
		var formattedDate = month + '/' + (day < 10 ? '0' + day : day)+'/'+year;
		// var formattedDate = (month < 10 ? '0' + month : month) + '/' + (day < 10 ? '0' + day : day)+'/'+year;
			
		if(title.value.indexOf('(')==-1){
			title.value=title.value+" ("+formattedDate+")"
		}
		// console.log(formattedDate)
	 }
	 
	const sList = []
	for (let i in data.list) {
		const item = data.list[i]
		for (let j in item.SizeList) {
			const child = item.SizeList[j]
			const s = child.size.toUpperCase()
			!sList.includes(s) && sList.push(s)
		}
	}
	sizeArr.value = sList
	// list.value = data.list
	list.value.splice(0)
	let flag=true;
	console.log(data.list)
	data.list.forEach(function(item,index){
		var name = item.name;
		var name = item.name;
		if(item.settleFlag=="1"){
			// name+="(已结算)"
		}
		flag=true;
		// if(!isEmpty(pColor.value)){
		// 	if(item.color.indexOf(pColor.value)<0){
		// 		flag=false;
		// 	}
		// }
		// if(!isEmpty(pPackgNum.value)){
		// 	if(item.croppingNum!=pPackgNum.value){
		// 		flag=false;
		// 	}
		// }
		if(!isEmpty(pCroppingID.value)){
			if(item.ID!=pCroppingID.value){
				flag=false;
			}
		}
		if(flag){
			item.SizeList.forEach(function(itemSL,indexSL){
				flag=true;
				if(!isEmpty(pSize.value)){
					if(itemSL.size.toUpperCase()!=pSize.value.toUpperCase()){
						flag=false;
					}
				}
				if(flag){
					list.value.push({
						...item,
						sizeID:itemSL.ID,
						size:itemSL.size,
						quantity:itemSL.quantity,
						ischecked:false,
					})
				}
			})
		}
	})
	console.log(list.value)
	quantity.value = data.quantity
	margin.value = data.margin
	
	console.log(idx!=undefined)
	if(idx!=undefined){
		let jobFinishList= await Https.Job.JobFinishList({
			companyId: companyID.value.toString(), 
			styleId: idx.toString()
		})
		listFinished.value=jobFinishList.ResData;
		console.log(jobFinishList)
		console.log(list.value)
	}
	console.log(list.value)
	if(list.value.length>0){
		// listTotal.value=list.value;
		listTotal.value.splice(0);
		list.value.forEach(function(item,index){
			if(item.ID==pCroppingID.value){
				pColor.value=item.color
				pStyle.value=item.style.name
				loadJobData(item, pSize.value) 
			}
			let temp = listTotal.value.find(itm => itm.color === item.color);
			let tempIndex = listTotal.value.findIndex(itm => itm.color === item.color);
			 // console.log(item)
			if(tempIndex==-1)
			{
				listTotal.value.push({
					Inventory:  item.Inventory,
					color:  item.color,
					croppingNum:  item.croppingNum,
					finisedPer:  item.finisedPer,
					length:  item.length,
					quantity:  item.quantity,
					remainder:  item.remainder,
					usage:  item.usage,
					SizeList:JSON.parse(JSON.stringify(item.SizeList))
				})
			}
			else{
				 // listTotal.value[tempIndex].SizeList[0].quantity +=item.SizeList[0].quantity;
				let flag=false;
				item.SizeList.forEach(function(im,idxs){
					let tempSize = listTotal.value[tempIndex].SizeList[idxs];
					if(tempSize.size===im.size){
						flag = true;
						listTotal.value[tempIndex].SizeList[idxs].quantity+=im.quantity;
					}
					if(!flag){
						listTotal.value[tempIndex].SizeList.push(im)
					}
					// listTotal.value[tempIndex].SizeList[idxs].quantity+=im.quantity;
				})
				// console.log(listTotal.value[tempIndex].SizeList[0])
			}
		})
		console.log(listTotal.value)
	}
}
const exChengeEvent=()=>{
	if(titleBtn.value=='汇总明细')
	{
		titleBtn.value='裁剪单';
		uni.setNavigationBarTitle({
			title: "查看汇总明细"
		});
	}
	else if(titleBtn.value=='裁剪单'){
		titleBtn.value='汇总明细';
		uni.setNavigationBarTitle({
			title: "查看裁剪单"
		});
	}
	// console.log(titleBtn.value)
}

const getUserList = async() => {
	let users= await Https.Staff.UserListByComp({
		companyId: companyID.value.toString(), 
		userName: "",
		roleName: ""
	})
	console.log(users)
	userSelectList.splice(0)
	users.ResData.forEach(function(item,index){
		let temp=false
		Ulist.value.forEach(function(itemExit,index){
			if(itemExit.uid==item.Id){
				temp = true
			}
		})
		if(!temp){
			userSelectList.push(item)
		}
		// userSelectList.push(item);
	})
	userSelectIndex.value = 0
	
	console.log(userSelectList)
}
const senBlData = (deviceId, serviceId, characteristicId,uint8Array) => {
		console.log('************deviceId = [' + deviceId + ']  serviceId = [' + serviceId + '] characteristics=[' +characteristicId+ "]")
		var uint8Buf = Array.from(uint8Array);
		function split_array(datas,size){
			var result = {};
			var j = 0
			for (var i = 0; i < datas.length; i += size) {
				result[j] = datas.slice(i, i + size)
				j++
			}
			console.log(result)
			return result
		}
		var sendloop = split_array(uint8Buf, 20);
		// console.log(sendloop.length)
		function realWriteData(sendloop, i) {
			var data = sendloop[i]
			if(typeof(data) == "undefined"){
				return
			}
			console.log("第【" + i + "】次写数据"+data)
			var buffer = new ArrayBuffer(data.length)
			var dataView = new DataView(buffer)
			for (var j = 0; j < data.length; j++) {
				dataView.setUint8(j, data[j]);
			}
			uni.writeBLECharacteristicValue({
				deviceId,
				serviceId,
				characteristicId,
				value: buffer,
				success(res) {
					realWriteData(sendloop, i + 1);
				},
				fail(obj) {
					/*
					console.log(obj)
					//重连
					uni.createBLEConnection({
						deviceId: deviceId,
						complete(res) {
							if (res.errMsg == "createBLEConnection:ok") {
								console.log("连接蓝牙-[]--成功")
								setTimeout(function() {
									// that.getBLEServices(item)
									
									console.log("获取蓝牙设备所有服务(service)。---------------")
									
									uni.getBLEDeviceServices({
										// 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
										deviceId: deviceId,
										complete(res) {
											console.log(res)
											let serviceId = ""
											for (var s = 0; s < res.services.length; s++) {
												console.log(res.services[s].uuid)
												let serviceId = res.services[s].uuid
												uni.getBLEDeviceCharacteristics({
													// 这里的 deviceId 需要已经通过 createBLEConnection 与对应设备建立链接
													deviceId: deviceId,
													// 这里的 serviceId 需要在 getBLEDeviceServices 接口中获取
													serviceId: serviceId,
													success(res) {
														var re = JSON.parse(JSON.stringify(res))
									senBleLabel();
														console.log('deviceId = [' + deviceId + ']  serviceId = [' + serviceId + ']')
														for (var c = 0; c < re.characteristics.length; c++) {
															if (re.characteristics[c].properties.write == true) {
																let uuid = re.characteristics[c].uuid
																console.log(' deviceId = [' + deviceId + ']  serviceId = [' + serviceId + '] characteristics=[' +
																	uuid)
									// 							for (var index in that.devices) {
									// 								if (that.devices[index].deviceId == deviceId) {
									// 									that.devices[index].services.push({
									// 										serviceId: serviceId,
									// 										characteristicId: uuid
									// 									})
									// 									break
									// 								}
									
									// 							}
																// uni.removeStorageSync("printParams");
																// uni.setStorageSync('printParams', JSON.stringify(item));
															}
														}
													}
												})
									
											}
									
									
									
										},
										fail(res) {
											console.log(res)
										},
									})
									uni.hideLoading();
									item.status="已连接"
								}, 2000)
							} else {
								console.log(res)
							}					
						},
					})
				*/
				}
			
			})
		}
	   var i = 0;
		realWriteData(sendloop, i);
	}
	const cancelEventTeam=()=>{
		isShow.value=false;
		clearTimeout(timer);
	}



const senBleLabel = async() => {
	// console.log(currPrinter.value)
	
	//  isShow.value=true;
	// list.value.forEach(function(itemD,indexD){
		
	// 	  timer =  setTimeout(() => {
	// 		GenQRCode();
	// 	      qrStyle.value=itemD.style.name;
	// 	      qrColor.value=itemD.color;
	// 	      qrSize.value = itemD.size;
	// 	      qrNumber.value=itemD.croppingNum;
	// 	      qrQuantity.value=itemD.quantity;
	// 		   console.log(1111)
	// 	     }, 1000 * indexD);
	// })
	// return;
	if(currPrinter.value=="设备未连接"){
		uni.showToast({
			title: '打印机未连接；定位未开',
			icon: 'error',
			duration: 2000
		})
		setTimeout(function(){
			connetEvent();
		},2000);
	}
	else{
		let hasData=false;
		list.value.forEach(function(item,index){
			console.log(item.ischecked)
			if(item.ischecked)
			{
				hasData=true;
			}
		});
		if(!hasData){
			uni.showToast({
				title: '请选择数据',
				icon: 'error',
				duration: 2000
			})
			return;
		}
		// isShow.value=true;
		let isShowLoading=true
		uni.showLoading({
			title: '打印中...',
			mask: true,
		});
		let currDev=null;
		let printerPobj=uni.getStorageSync("printParams");
		 console.log(printerPobj) 
		if(printerPobj){
			 currDev=JSON.parse(printerPobj);
		}
		//标签模式
		let deviceId = currDev.deviceId;
		let serviceId = currDev.services[0].serviceId;
		let characteristicId = currDev.services[0].characteristicId;
		let i=0;
		isShow.value=true;
		list.value.forEach(function(itemD,indexD){
			i++;
			if(itemD.ischecked){
			  let qrData=itemD.ID+";"+itemD.style.name+";"+itemD.croppingNum+";"+itemD.size+";"+itemD.quantity+";"+itemD.color;
			  timer =  setTimeout(() => {
				  GenQRCode(qrData);
			      qrStyle.value=itemD.style.name;
			      qrColor.value=itemD.color;
			      qrSize.value = itemD.size;
			      qrNumber.value=itemD.croppingNum;
			      qrQuantity.value=itemD.quantity;
				   // 处理长款式名称换行
				   const splitTextForPrint = (text, maxCharsPerLine = 10) => {
					   if (!text || text.length <= maxCharsPerLine) {
						   return [text || '']
					   }
					   
					   const lines = []
					   let currentLine = ''
					   let currentLength = 0
					   
					   for (let i = 0; i < text.length; i++) {
						   const char = text[i]
						   // 中文字符占2个字符位，英文数字占1个字符位
						   const charWidth = /[\u4e00-\u9fa5]/.test(char) ? 2 : 1
						   
						   if (currentLength + charWidth > maxCharsPerLine && currentLine.length > 0) {
							   lines.push(currentLine)
							   currentLine = char
							   currentLength = charWidth
						   } else {
							   currentLine += char
							   currentLength += charWidth
						   }
					   }
					   
					   if (currentLine) {
						   lines.push(currentLine)
					   }
					   
					   return lines
				   }

				   // 检查是否有订单号（款式名称包含/）
				   const hasOrderNum = qrStyle.value && qrStyle.value.includes('/');
				   const orderNum = hasOrderNum ? qrStyle.value.split('/')[1] || '' : '';
				   // 款式名称只取"/"前面的部分
				   const styleName = hasOrderNum ? qrStyle.value.split('/')[0] : qrStyle.value;
				   
				   const styleLines = splitTextForPrint(styleName, 10);
				   
				   var command = tsc.createNew()
				   command.setSize(40, 130)
				   command.setGap(2)
				   command.setCls()
				   
				   // 智能布局计算（扫描页面用130mm高度）
				   const orderLineCount = hasOrderNum ? 1 : 0; // 订单号行数
				   const estimatedLines = styleLines.length + orderLineCount + 4; // 款式、订单(可选)、包号、颜色、尺码、件数
				   const baseLineSpacing = estimatedLines > 7 ? 30 : 35; // 调整行间距，避免重叠
				   const styleLineSpacing = 35; // 款式换行专用间距
				   
				   // 款式（可能多行）
				   let yPos = 40;
				   styleLines.forEach((line, lineIndex) => {
					   const prefix = lineIndex === 0 ? "款式：" : "　　　";
					   command.setText(30, yPos, "TSS24.BF2", 1, 1, prefix + line);
					   yPos += styleLineSpacing; // 使用专用的款式行间距
				   });
				   
				   // 订单号（如果存在）
				   if (hasOrderNum && orderNum) {
					   command.setText(30, yPos, "TSS24.BF2", 1, 1, "订单：" + orderNum);
					   yPos += baseLineSpacing;
				   }
				   
				   // 其他信息
				   command.setText(30, yPos, "TSS24.BF2", 1, 1, "包号："+qrNumber.value)
				   yPos += baseLineSpacing;
				   command.setText(30, yPos, "TSS24.BF2", 1, 1, "颜色："+qrColor.value)
				   yPos += baseLineSpacing;
				   command.setText(30, yPos, "TSS24.BF2", 1, 1, "尺码："+qrSize.value)
				   yPos += baseLineSpacing;
				   command.setText(30, yPos, "TSS24.BF2", 1, 1, "件数："+qrQuantity.value)
				   yPos += baseLineSpacing + 15;
				   
				   // 二维码位置（左侧，扫描页面用较大的二维码）
				   const qrY = Math.min(yPos, 380); // 稍微上移，确保不超出标签
				   command.setQR(30, qrY, "H", 6, "A", qrData)
				   
				   // "睿睿计件系统"竖排显示在二维码右侧，增加间距
				   command.setText(280, qrY + 15, "TSS24.BF2", 1, 1, "睿");
				   command.setText(280, qrY + 40, "TSS24.BF2", 1, 1, "睿");
				   command.setText(280, qrY + 65, "TSS24.BF2", 1, 1, "计");
				   command.setText(280, qrY + 90, "TSS24.BF2", 1, 1, "件");
				   command.setText(280, qrY + 115, "TSS24.BF2", 1, 1, "系");
				   command.setText(280, qrY + 140, "TSS24.BF2", 1, 1, "统");
				   command.setPagePrint()
				   senBlData(deviceId, serviceId, characteristicId,command.getData())
				   // console.log(indexD)
				   // console.log(list.value.length-1)
				   // if(i==list.value.length){
					  //  console.log("停了")
					  // isShow.value=false;
				   // }
			     }, 500 * indexD);
			}
		})
		
		isShowLoading&&uni.hideLoading()
		// let _that = this;
		// let i=0;
		// setTimeout(()=>{ 
		// 	if(i<5){
		// 		_that.senBlData(deviceId, serviceId, characteristicId,command.getData())
		// 		i++;
		// 	}
		// },1000)
		// this.senBlData(deviceId, serviceId, characteristicId,command.getData())
	}
}
const sleep=async(seconds)=> {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}
onLoad(({ id,styleid,sizeid, name,pknum,size,num,color }) => {
    // 加载工序缓存
    loadProcessCache()

	name && (styleName = name,pStyle.value=name)
	id && (pCroppingID.value = id) 
	styleid && (idx = styleid) 
	styleid && (pStyleID.value = styleid) 
	sizeid && (pSizeID.value = sizeid) 
	pknum && (pPackgNum.value = pknum) 
	size && (pSize.value = size) 
	num && (pQuantity.value = num,pQuantityPre.value=num) 
	color && (pColor.value = color) 
	console.log("id,styleid,sizeid, name,pknum,size,num,color=="+id+"="+styleid+"="+sizeid+"="+ name+"="+pknum+"="+size+"="+num+"="+color)
	title.value = styleName
	uni.setNavigationBarTitle({
		title: "扫菲【"+name+"】"
	});
	let printerPobj=uni.getStorageSync("printParams");
	let printerParams=JSON.parse(printerPobj);
	if(printerParams){
		currPrinter = printerParams.name
	}
    // 调用原有的数据加载方法
	getUser()
	getProcess()
	getUserList()
})
onShow(_search)

// 添加防重复提交的状态控制变量
const isSubmitting = ref(false)
</script>

<style lang="scss" scoped>
.process-tag {
    font-size: 20rpx;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    margin-left: 10rpx;
}

.process-tag.cached {
    background-color: #e6f7ff;
    color: #1890ff;
    border: 1rpx solid #91d5ff;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header-action {
    font-size: 24rpx;
    color: #1890ff;
    margin-left: 20rpx;
}
/* 页面容器 */
.page-container {
	min-height: 100vh;
	background-color: #f5f7fa;
	padding: 20rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 120rpx);
	padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
}

/* 卡片通用样式 */
.info-card, .process-card, .option-card {
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
	overflow: hidden;
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f2f5;
}

.header-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1a1a1a;
}

.header-action {
	font-size: 28rpx;
	color: #007aff;
	font-weight: 500;
}

/* 基本信息列表 */
.info-list {
	padding: 0 32rpx 32rpx 32rpx;
}

.info-item {
	display: flex;
	align-items: center;
	padding: 24rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
	
	&:last-child {
		border-bottom: none;
	}
}

.info-label {
	font-size: 30rpx;
	color: #666666;
	flex-shrink: 0;
	margin-right: 20rpx;
}

.info-value {
	font-size: 30rpx;
	color: #1a1a1a;
	font-weight: 500;
	flex: 1;
}

.info-input {
	font-size: 30rpx;
	color: #1a1a1a;
	background-color: #f8f9fa;
	padding: 16rpx 20rpx;
	border-radius: 8rpx;
	border: 1rpx solid #e9ecef;
	width: 200rpx;
	margin-right: 16rpx;
}

.tips-label {
	font-size: 24rpx;
	color: #ff4757;
	flex-shrink: 0;
}

/* 工序列表 */
.process-list {
	padding: 0 32rpx 24rpx 32rpx;
}

.process-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
	
	&:last-child {
		border-bottom: none;
	}
}

.process-info {
	flex: 1;
}

.process-name {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 500;
}

.process-action {
	flex-shrink: 0;
}

.receive-btn {
	background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
	color: #ffffff;
	border: none;
	border-radius: 12rpx;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
	font-weight: 500;
	box-shadow: 0 2rpx 6rpx rgba(0, 122, 255, 0.3);
	min-width: 80rpx;
	
	&:active {
		transform: translateY(1rpx);
		box-shadow: 0 1rpx 4rpx rgba(0, 122, 255, 0.3);
	}
	
	&.disabled {
		background: #d1d5db;
		color: #9ca3af;
		box-shadow: none;
		transform: none;
	}
}

.completed-text {
	font-size: 24rpx;
	color: #ffa726;
	background-color: #fff3e0;
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	font-weight: 500;
	min-width: 80rpx;
	text-align: center;
}

/* 操作选项卡片 */
.option-card {
	padding: 32rpx;
}

.checkbox-item {
	margin-bottom: 24rpx;
}

.custom-checkbox {
	font-size: 28rpx;
	color: #666666;
}

.cropping-link {
	text-align: center;
}

.link-text {
	font-size: 32rpx;
	color: #007aff;
	font-weight: 500;
	background-color: #f0f8ff;
	padding: 20rpx 40rpx;
	border-radius: 12rpx;
	display: inline-block;
}

/* 底部按钮区 */
.bottom-actions {
	position: fixed;
	left: 20rpx;
	right: 20rpx;
	bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
	bottom: calc(env(safe-area-inset-bottom) + 20rpx);
	display: flex;
	gap: 20rpx;
	background-color: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	padding: 20rpx;
	border-radius: 16rpx;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.primary-btn {
	flex: 2;
	background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	height: 88rpx;
	font-size: 32rpx;
	font-weight: 600;
	box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.4);
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.4);
	}
}

.disabled-btn {
	flex: 2;
	background-color: #e9ecef;
	color: #adb5bd;
	border: none;
	border-radius: 16rpx;
	height: 88rpx;
	font-size: 32rpx;
	font-weight: 600;
}

.secondary-btn {
	flex: 1;
	background-color: #f8f9fa;
	color: #495057;
	border: 2rpx solid #dee2e6;
	border-radius: 16rpx;
	height: 88rpx;
	font-size: 32rpx;
	font-weight: 500;
	
	&:active {
		background-color: #e9ecef;
	}
}

/* 下滑指示器样式 */
.swipe-indicator {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 16rpx 0 8rpx;
	background-color: white;
}

.swipe-bar {
	width: 80rpx;
	height: 8rpx;
	background-color: #d1d5db;
	border-radius: 8rpx;
	transition: background-color 0.2s ease;
}

/* 打印弹窗样式 */
.print-modal {
	background-color: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	max-height: 80vh;
}

.modal-header {
	padding: 40rpx 32rpx 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f2f5;
	text-align: center;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #1a1a1a;
}

.modal-content {
	padding: 32rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.print-info {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.print-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.print-label {
	font-size: 28rpx;
	color: #666666;
	width: 120rpx;
	flex-shrink: 0;
}

.print-value {
	font-size: 28rpx;
	color: #1a1a1a;
	font-weight: 500;
	flex: 1;
	text-align: right;
}

.qr-container {
	display: flex;
	justify-content: center;
	padding: 40rpx 0;
	border-bottom: none;
}

.qrcode {
	width: 400rpx;
	height: 400rpx;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.modal-actions {
	display: flex;
	justify-content: center;
	padding-top: 32rpx;
}

.cancel-print-btn {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
	color: #ffffff;
	border: none;
	border-radius: 16rpx;
	padding: 20rpx 60rpx;
	font-size: 30rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 12rpx rgba(255, 107, 107, 0.3);
	}
}

/* 响应式适配 */
@media (max-width: 750rpx) {
	.page-container {
		padding: 16rpx;
	}
	
	.card-header, .info-list, .process-list {
		padding-left: 24rpx;
		padding-right: 24rpx;
	}
	
	.bottom-actions {
		left: 16rpx;
		right: 16rpx;
		padding: 16rpx;
	}
}

/* 兼容之前的样式，避免冲突 */
.printer, .search, .search-single, .btnList,
.printerSet, .printerSetTitle, .printerSetContent,
.colorLink, .queryCropping, .receveFlag, .colorLinkLarge,
.txtContent, .table, .scrollView, .scrollShowAll,
.total, .contentCount, .detail, .title, .titleView,
.totleDetial, .txtNotion, .btnGroup, .addLink, .addLinkCJD,
.group, .task, .child_with, .txtNomal, .txtDoing,
.txtAudit, .txtFinish, .optionClass, .titleStr,
.pickerView, .uContent, .disabled, .edit, .reback,
.addBtn, .submitBottom, .printTitle, .btnsave, .jobs {
	display: none !important;
}
</style>
