/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pagination[data-v-2a6448d5] {
  padding-top: 0.625rem;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  max-height: 24vh;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #ffffff;
  box-shadow: 0 0.1875rem 0.375rem 0.125rem rgba(0, 0, 0, 0.05), 0 0.5rem 0.3125rem 0.0625rem rgba(0, 0, 0, 0.06), 0 0.3125rem 0.3125rem -0.1875rem rgba(0, 0, 0, 0.05);
}
.pagination .total[data-v-2a6448d5] {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  height: 0.625rem;
  padding-bottom: 0.9375rem;
  border-bottom: 1px solid gray;
}
.pagination .totalTitle[data-v-2a6448d5] {
  white-space: pre-wrap;
  text-align: center;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.875rem;
  padding-top: 0.9375rem;
}
.pagination .totalTitle .scrollPagination[data-v-2a6448d5] {
  height: 24vh;
  padding: 0;
  margin: 0;
  width: 14.0625rem;
}
.pagination .totalTitle .scrollPagination .totalTitleSub[data-v-2a6448d5] {
  width: 14.0625rem;
  text-align: left;
  margin-left: 0;
}
.pagination .pagin[data-v-2a6448d5] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
  height: 2.5rem;
}
.pagination .pagin .text[data-v-2a6448d5] {
  font-size: 1rem;
}
.pagination .pagin .text .current[data-v-2a6448d5] {
  color: #007aff;
}
.mask[data-v-2a6448d5] {
  height: 3.75rem;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.txtNum[data-v-2a6448d5] {
  color: #007aff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.issue[data-v-becdbe06] {
  position: fixed;
  top: var(--window-top);
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  pointer-events: none;
}
.issue .target[data-v-becdbe06] {
  pointer-events: all;
  pointer-events: x;
  width: 0;
  height: 0;
}
.issue .target .icon[data-v-becdbe06] {
  display: block;
  width: 100%;
  height: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.issue[data-v-8c923a7d] {
  position: fixed;
  top: var(--window-top);
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  pointer-events: none;
}
.issue .target[data-v-8c923a7d] {
  pointer-events: auto;
  pointer-events: x;
  width: 2.8125rem;
  height: 2.8125rem;
  border-radius: 50%;
  background-color: #007aff;
}
.issue .target .icon[data-v-8c923a7d] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.8125rem;
  height: 2.8125rem;
  color: #fff;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.modal[data-v-4bcd74eb] {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  outline: 0;
  text-align: center;
  transform: scale(1.185);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  perspective: 62.5rem;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}
.modal[data-v-4bcd74eb]::before {
  content: "​";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.modal.show[data-v-4bcd74eb] {
  opacity: 1;
  transition-duration: 0.3s;
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}
.modal .dialog[data-v-4bcd74eb] {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 21.25rem;
  max-width: 100%;
  background-color: #f8f8f8;
  border-radius: 0.3125rem;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
}
.modal.bottom[data-v-4bcd74eb] {
  margin-bottom: -31.25rem;
}
.modal.bottom[data-v-4bcd74eb]::before {
  vertical-align: bottom;
}
.modal.bottom.show[data-v-4bcd74eb] {
  margin-bottom: 0;
}
.modal.bottom .dialog[data-v-4bcd74eb] {
  width: 100%;
  border-radius: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.empty[data-v-e2280098] {
  display: grid;
  place-items: center;
  min-height: 18.75rem;
  color: #999;
  font-size: 1rem;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.title[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3.125rem;
  font-weight: bold;
}
.titleView[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  gap: 0.625rem;
}
.totleDetial[data-v-fd42dd3d] {
  color: white;
  background-color: #007aff;
  font-size: 0.75rem;
  text-align: left;
  margin-left: 0.625rem;
  padding: 0.09375rem 0.3125rem 0.1875rem 0.3125rem;
  height: 1.5625rem;
  line-height: 1.25rem;
  margin-bottom: 0.3125rem;
}
.txtNotion[data-v-fd42dd3d] {
  color: red;
  font-size: 0.75rem;
  text-align: right;
  padding: 0.3125rem;
  padding-right: 0.625rem;
}
.editBtn[data-v-fd42dd3d] {
  color: white;
  background-color: #28a745;
  font-size: 0.75rem;
  padding: 0.09375rem 0.3125rem 0.1875rem 0.3125rem;
  height: 1.5625rem;
  line-height: 1.25rem;
  border-radius: 0.1875rem;
  border: none;
}
.editNotion[data-v-fd42dd3d] {
  color: #28a745;
  font-size: 0.75rem;
  text-align: right;
  padding: 0.3125rem;
  padding-right: 0.625rem;
}
.edit-actions[data-v-fd42dd3d] {
  position: fixed;
  bottom: 3.125rem;
  left: 0;
  right: 0;
  padding: 0.625rem;
  background-color: rgba(255, 255, 255, 0.95);
  border-top: 1px solid #e5e7eb;
  z-index: 999;
}
.delete-btn[data-v-fd42dd3d] {
  width: 100%;
  height: 2.5rem;
  background-color: #ff4757;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 1rem;
  font-weight: bold;
}
.delete-btn[data-v-fd42dd3d]:disabled {
  background-color: #ccc;
  color: #999;
}
.table[data-v-fd42dd3d] {
  overflow-x: auto;
}
.table[data-v-fd42dd3d]:not(:last-child) {
  margin-bottom: 0.625rem;
}
.table table[data-v-fd42dd3d] {
  text-align: center;
  border-spacing: 0;
  border-collapse: collapse;
  background-color: #ffffff;
  table-layout: fixed;
}
.table table td[data-v-fd42dd3d] {
  text-align: center;
  font-size: 0.75rem;
  padding: 0.375rem 0;
  border: 1px solid #e5e7eb;
}
.table table tr.header[data-v-fd42dd3d] {
  background-color: #f5f9ff;
}
.table table tr.stripe[data-v-fd42dd3d]:nth-child(even) {
  background-color: #f5f9ff;
}
.table table tr.stripe.selected[data-v-fd42dd3d] {
  background-color: #e3f2fd !important;
  border: 2px solid #2196f3;
}
.scrollView[data-v-fd42dd3d] {
  height: 45vh;
  padding: 0;
  margin: 0;
}
.scrollShowAll[data-v-fd42dd3d] {
  max-height: 79vh;
  padding: 0;
  margin: 0;
}
.total[data-v-fd42dd3d] {
  padding: 0.625rem;
  font-size: 0.875rem;
}
.contentCount[data-v-fd42dd3d] {
  line-height: 1.875rem !important;
}
.detail[data-v-fd42dd3d] {
  background-color: #ffffff;
}
.detail .title[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.5rem;
  padding: 1.25rem 0 0;
  border-bottom: 0.03125rem solid #e5e7eb;
  font-weight: bold;
  position: relative;
}
.detail .title .status[data-v-fd42dd3d] {
  font-size: 0.8125rem;
  font-weight: normal;
}
.detail .title .status.success[data-v-fd42dd3d] {
  color: #007aff;
}
.detail .title .status.fail[data-v-fd42dd3d] {
  color: #dd524d;
}
.detail .cont .jobs[data-v-fd42dd3d] {
  font-size: 0.875rem;
}
.detail .cont .jobs .row[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail .cont .jobs .row[data-v-fd42dd3d]:nth-child(odd) {
  background-color: #f5f9ff;
}
.detail .cont .jobs .row .cell[data-v-fd42dd3d]:nth-child(1) {
  width: 5.625rem;
}
.detail .cont .jobs .row .cell[data-v-fd42dd3d]:nth-child(2) {
  flex: 1;
}
.detail .cont .jobs .row .cell .item[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail .cont .jobs .row .cell .item .child[data-v-fd42dd3d] {
  padding: 0.3125rem 0.1875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.detail .cont .jobs .row .cell .item .child[data-v-fd42dd3d]:nth-child(1), .detail .cont .jobs .row .cell .item .child[data-v-fd42dd3d]:nth-child(2) {
  width: 3.125rem;
}
.detail .cont .jobs .row .cell .item .child[data-v-fd42dd3d]:nth-child(3) {
  flex: 1;
}
.detail .cont .jobs .row .cell .item .child .name[data-v-fd42dd3d] {
  flex: 1;
}
.detail .cont .jobs .row .cell .item .child .name .primary[data-v-fd42dd3d] {
  color: #007aff;
}
.btnGroup[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
}
.btnGroup .addLink[data-v-fd42dd3d] {
  width: 3.4375rem;
  margin: 0.25rem;
  padding: 0.03125rem !important;
}
.btnGroup .addLinkCJD[data-v-fd42dd3d] {
  width: 4.375rem;
  margin: 0.25rem;
  padding: 0.03125rem !important;
}
.btnGroup .close-btn[data-v-fd42dd3d] {
  position: absolute;
  top: 0.3125rem;
  right: 0.625rem;
  width: 1.5625rem;
  height: 1.5625rem;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  color: #666;
  z-index: 10;
}
.btnGroup .close-btn[data-v-fd42dd3d]:active {
  background-color: #e0e0e0;
  color: #333;
}
.btnGroup .close-btn uni-text[data-v-fd42dd3d] {
  line-height: 1;
}
.group[data-v-fd42dd3d] {
  --label: 1.875rem;
  display: flex;
  align-items: center;
}
.group .item[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3.75rem;
  border-bottom: 0.03125rem solid #e5e7eb;
}
.group .item.delete-item[data-v-fd42dd3d] {
  color: #ff4757;
  background-color: #fff5f5;
}
.group .item.delete-item[data-v-fd42dd3d]:active {
  background-color: #ffe0e0;
}
.group .num[data-v-fd42dd3d] {
  width: 2.5rem;
  flex-shrink: 0;
}
.group .btn[data-v-fd42dd3d] {
  width: 3.75rem;
  padding: 0;
}
.task .name[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.625rem;
  height: 3.125rem;
}
.task .form[data-v-fd42dd3d] {
  max-height: 70vh;
  overflow-y: auto;
}
.task .submit[data-v-fd42dd3d] {
  display: flex;
  margin: 0.625rem;
}
.child_with[data-v-fd42dd3d] {
  width: 33%;
}
.txtDoing[data-v-fd42dd3d] {
  color: orange;
}
.txtAudit[data-v-fd42dd3d] {
  color: red;
}
.txtFinish[data-v-fd42dd3d] {
  color: green;
}
.titleStr[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1.875rem;
  font-weight: bold;
}
.uContent[data-v-fd42dd3d] {
  max-height: 60vh;
  overflow-y: scroll;
  display: block;
  padding: 0.25rem;
  margin: 0.3125rem;
  border-radius: 0.625rem;
}
.uContent .item[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 3.125rem;
}
.uContent .item .label[data-v-fd42dd3d] {
  display: block;
  width: 2.6875rem;
}
.uContent .item .picker[data-v-fd42dd3d] {
  width: 4.6875rem !important;
}
.uContent .item .input[data-v-fd42dd3d] {
  flex: 1;
  background-color: #e1e5ea;
  font-size: 0.875rem;
  height: 1.875rem;
  width: 2.46875rem;
}
.uContent .item .input .value[data-v-fd42dd3d]:empty::before {
  content: "请选择";
  color: gray;
}
.uContent .submit[data-v-fd42dd3d] {
  display: flex;
  height: 2.5rem;
  background-color: #007aff;
  color: #fff;
  margin-top: 1.5625rem;
}
.btnList[data-v-fd42dd3d] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 3.125rem;
  padding: 0 0.625rem;
  border-bottom: 0.03125rem solid #e5e7eb;
}
.edit[data-v-fd42dd3d] {
  background-color: #007aff;
  color: #fff;
  height: 2.5rem;
  left: 0.625rem;
  width: 46%;
}
.reback[data-v-fd42dd3d] {
  background-color: #e5e5e5;
  border: 1px solid #e5e5e5;
  height: 2.5rem;
  right: 0.625rem;
  width: 46%;
}
.addBtn[data-v-fd42dd3d] {
  width: 66%;
}