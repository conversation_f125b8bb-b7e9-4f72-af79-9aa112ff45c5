"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var at=Object.create;var ke=Object.defineProperty;var it=Object.getOwnPropertyDescriptor;var lt=Object.getOwnPropertyNames;var nt=Object.getPrototypeOf,st=Object.prototype.hasOwnProperty;var Le=(e,i)=>()=>(i||e((i={exports:{}}).exports,i),i.exports);var rt=(e,i,n,u)=>{if(i&&typeof i=="object"||typeof i=="function")for(let m of lt(i))!st.call(e,m)&&m!==n&&ke(e,m,{get:()=>i[m],enumerable:!(u=it(i,m))||u.enumerable});return e};var ie=(e,i,n)=>(n=e!=null?at(nt(e)):{},rt(i||!e||!e.__esModule?ke(n,"default",{value:e,enumerable:!0}):n,e));var xe=(e,i,n)=>new Promise((u,m)=>{var r=h=>{try{y(n.next(h))}catch(w){m(w)}},v=h=>{try{y(n.throw(h))}catch(w){m(w)}},y=h=>h.done?u(h.value):Promise.resolve(h.value).then(r,v);y((n=n.apply(e,i)).next())});var ue=Le((Lt,De)=>{De.exports=Vue});var de=Le((xt,Ge)=>{Ge.exports=uni.Pinia});var t=ie(ue()),Be=ie(de()),ut="onShow",dt="onHide",ct="onLoad",pt="onReady",gt="onUnload",vt="onNavigationBarButtonTap";function A(e,i,...n){uni.__log__?uni.__log__(e,i,...n):console[e].apply(console,[...n,i])}function ce(e,i){return typeof e=="string"?i:e}var Y=(e,i=0)=>(n,u=(0,t.getCurrentInstance)())=>{!t.isInSSRComponentSetup&&(0,t.injectHook)(e,n,u)},pe=Y(ut,3),Re=Y(dt,3),Ie=Y(ct,2),ht=Y(pt,2),Ue=Y(gt,2),qe=Y(vt,2),ft={"ml-player":{"":{position:"relative",zIndex:0}},rateIcon:{".ml-player ":{position:"absolute",right:3,top:95,zIndex:1}},rateText:{".ml-player .rateIcon ":{fontSize:11,color:"#ffffff",backgroundColor:"rgba(0,0,0,0.5)",paddingTop:"5rpx",paddingRight:"8rpx",paddingBottom:"5rpx",paddingLeft:"8rpx",borderRadius:"8rpx",height:22,lineHeight:22}},"center-play-mask":{".ml-player ":{position:"fixed",top:0,left:0,alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)"}},"center-play-btn":{".ml-player .center-play-mask ":{width:"140rpx",height:"140rpx"}},"center-loading":{".ml-player .center-play-mask ":{width:"100rpx",height:"100rpx"}}},le=(e,i)=>{let n=e.__vccOpts||e;for(let[u,m]of i)n[u]=m;return n},mt=Object.assign({name:"MlPlayer"},{__name:"ml-player",props:{showPlayer:{type:Boolean,default:!0},video:{type:Object,default:{},required:!0},danmu:{danmuList:{type:Array,default:[]},danmuBtn:{type:Boolean,default:!1},enableDanmu:{type:Boolean,default:!1}},videoOptions:{type:Object,default:{width:0,height:0,fillHeight:!1,controls:!0,autoplay:!0,loop:!0,muted:!1,initialTime:0,duration:0,showPoster:!0,showProgress:!0,showCenterPlayBtn:!0,enablePlayGesture:!1,showLoading:!0,enableProgressGesture:!0,objectFit:"contain",playBtnPosition:"center",mobilenetHintType:1,autoPauseIfNavigate:!0,autoPauseIfOpenNative:!0,vslideGesture:!0,vslideGestureInFullscreen:!0,codec:"hardware",httpCache:!0,playStrategy:2,header:{},isLive:!1,showRate:!0,showFit:!0,rateList:["0.5","0.8","1.0","1.25","1.5","2.0"],enableClick:!1,enableDblClick:!1,showWaitingTips:!0,waitingCount:5,waitingMessage:"\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73"}}},emits:["videoClick","doubleClick","play","pause","ended","timeupdate","error","fullscreenchange","waiting","progress","loadedmetadata","fullscreenclick","controlstoggle","playVideo"],setup(e,{emit:i}){var Pe,Se,Te,Oe;var n,u,m,r,v,y,h,w,P,b,T,C,R,z,B,_,Q,F,H,K,Z,X,W,I,U,S,l,s,k,q,M,j,V,ve,he;let d=e,x=(0,t.ref)(!1),se=uni.getSystemInfoSync(),oe=(0,t.ref)(((n=d.videoOptions)==null?void 0:n.width)||se.windowWidth),J=(0,t.ref)(((u=d.videoOptions)==null?void 0:u.height)||Math.ceil(se.windowHeight*.33)),fe=(0,t.ref)(J.value);((m=d.videoOptions)==null?void 0:m.fillHeight)==!0&&(J.value=se.windowHeight,fe.value=J.value);let p=(0,t.ref)({width:oe.value,height:J.value,controls:((r=d.videoOptions)==null?void 0:r.controls)!=!1,autoplay:((v=d.videoOptions)==null?void 0:v.autoplay)!=!1,loop:((y=d.videoOptions)==null?void 0:y.loop)!=!1,muted:((h=d.videoOptions)==null?void 0:h.muted)==!0,initialTime:(Pe=(w=d.videoOptions)==null?void 0:w.initialTime)!=null?Pe:0,duration:(Se=(P=d.videoOptions)==null?void 0:P.duration)!=null?Se:0,showPoster:((b=d.videoOptions)==null?void 0:b.showPoster)!=!1,showProgress:((T=d.videoOptions)==null?void 0:T.showProgress)!=!1,showCenterPlayBtn:!1,enablePlayGesture:((C=d.videoOptions)==null?void 0:C.enablePlayGesture)==!0,showLoading:((R=d.videoOptions)==null?void 0:R.showLoading)!=!1,enableProgressGesture:((z=d.videoOptions)==null?void 0:z.enableProgressGesture)!=!1,objectFit:((B=d.videoOptions)==null?void 0:B.objectFit)||"contain",playBtnPosition:((_=d.videoOptions)==null?void 0:_.playBtnPosition)||"center",mobilenetHintType:(Te=(Q=d.videoOptions)==null?void 0:Q.mobilenetHintType)!=null?Te:1,autoPauseIfNavigate:((F=d.videoOptions)==null?void 0:F.autoPauseIfNavigate)!=!1,autoPauseIfOpenNative:((H=d.videoOptions)==null?void 0:H.autoPauseIfOpenNative)!=!1,vslideGesture:((K=d.videoOptions)==null?void 0:K.vslideGesture)!=!1,vslideGestureInFullscreen:((Z=d.videoOptions)==null?void 0:Z.vslideGestureInFullscreen)!=!1,codec:((X=d.videoOptions)==null?void 0:X.codec)||"hardware",httpCache:((W=d.videoOptions)==null?void 0:W.httpCache)!=!1,playStrategy:(Oe=(I=d.videoOptions)==null?void 0:I.playStrategy)!=null?Oe:2,header:((U=d.videoOptions)==null?void 0:U.header)||{},isLive:((S=d.videoOptions)==null?void 0:S.isLive)==!0,showRate:((l=d.videoOptions)==null?void 0:l.showRate)!=!1,rateList:((s=d.videoOptions)==null?void 0:s.rateList)||["0.5","0.8","1.0","1.25","1.5","2.0"],enableClick:((k=d.videoOptions)==null?void 0:k.enableClick)==!0,enableDblClick:((q=d.videoOptions)==null?void 0:q.enableDblClick)==!0,showWaitingTips:((M=d.videoOptions)==null?void 0:M.showWaitingTips)!=!1,waitingCount:((j=d.videoOptions)==null?void 0:j.waitingCount)||5,waitingMessage:((V=d.videoOptions)==null?void 0:V.waitingMessage)||"\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73",fillHeight:((ve=d.videoOptions)==null?void 0:ve.fillHeight)==!0,showFit:((he=d.videoOptions)==null?void 0:he.showFit)!=!1}),me=(0,t.getCurrentInstance)(),g=null,ye=(0,t.ref)(1),$=(0,t.ref)(0),be=(0,t.ref)(null),E=(0,t.ref)(!1),we=(0,t.computed)(()=>d.showPlayer!=!1),Ce=0,re=0,O=i;ht(()=>{(!g||g==null)&&setTimeout(()=>{N()},500)});let N=()=>{var f;let L=!g||g==null,D=d.video||{};return L&&D&&D.url&&((f=D.url)==null?void 0:f.length)>0&&(g=uni.createVideoContext("ml-player",me)),g},Me=()=>{N(),E.value=!0,x.value=!1,O("play",g)},je=()=>{N(),E.value=!1,x.value=!1,O("pause",g)},Ve=()=>{N(),$.value=0,x.value=!1,O("ended",g)},Je=()=>{N(),g&&(g==null||g.play(),E.value=!0,x.value=!1),O("playVideo",g)},ze=f=>{E.value=!0,x.value=!1,O("timeupdate",g,f)};function _e(f){O("fullscreenchange",g,f)}let Qe=()=>{if(x.value=!0,p.value.showWaitingTips){if($.value>=p.value.waitingCount)return ae(p.value.waitingMessage,"none"),$.value=0,!1;$.value=$.value+1}N(),O("waiting",g)},Fe=()=>{let f=["\u5305\u542B","\u586B\u5145","\u8986\u76D6"];uni.showActionSheet({title:"\u8BBE\u7F6E\u5C55\u793A\u5F62\u5F0F",alertText:"\u9009\u62E9\u5C55\u793A\u5F62\u5F0F",itemList:f,success:function(L){let D=L.tapIndex,ot=["contain","fill","cover"][D];p.value.objectFit=ot,ae("\u5C55\u793A\u5F62\u5F0F\uFF1A"+f[D],"none")}})},He=()=>{let f=p.value.rateList||["0.5","0.75","1.0","1.25","1.5","1.75","2.0","2.5"];f&&f.length>0&&uni.showActionSheet({title:"\u8BBE\u7F6E\u500D\u901F",alertText:"\u9009\u62E9\u500D\u901F",itemList:f,success:function(L){let D=Number(f[L.tapIndex]);g||N(),g&&!isNaN(D)&&(ye.value=D,g==null||g.playbackRate(D),ae("\u500D\u901F\uFF1A"+D,"none"))}})},Ke=f=>{ae("\u8D44\u6E90\u64AD\u653E\u9519\u8BEF","error"),E.value=!1,x.value=!1;let L={video:d.video,error:f};A("error","at components/ml-player/ml-player.vue:387","==========\u8D44\u6E90\u64AD\u653E\u9519\u8BEF========="),A("error","at components/ml-player/ml-player.vue:388",L),O("error",g,f)};function Ze(f){O("progress",g,f)}function Xe(f){O("loadedmetadata",g,f)}function Ye(f){O("fullscreenclick",g,f)}function $e(f){O("controlstoggle",g,f)}let et=()=>{p.value.enableClick==!0&&(N(),O("videoClick",g,d.video))},tt=()=>{let f=Date.now(),L=Ce;Ce=f,f-L<200?(clearTimeout(re),p.value.enableDblClick==!0&&O("doubleClick",g,d.video)):re=setTimeout(()=>{et()},200)},ae=(f,L)=>{uni.hideToast(),uni.showToast({title:f,icon:L||"none",mask:!1,duration:2e3})};function Ae(){g&&(g==null||g.pause(),g==null||g.stop()),clearTimeout(re),g=null,be.value=null,p.value=null,me=null}return Re(()=>{g&&(g==null||g.pause())}),Ue(()=>{Ae()}),(0,t.onUnmounted)(()=>{Ae()}),(f,L)=>((0,t.openBlock)(),(0,t.createElementBlock)(t.Fragment,null,[we.value&&e.video&&e.video.url?((0,t.openBlock)(),(0,t.createElementBlock)("u-video",{key:0,class:"ml-player",id:"ml-player",ref_key:"mlPlayer",ref:be,src:e.video.url,poster:e.video.poster,title:e.video.title,controls:p.value.controls,autoplay:p.value.autoplay,loop:p.value.loop,muted:p.value.muted,initialTime:p.value.initialTime,duration:p.value.duration,showFullscreenBtn:p.value.controls,showPlayBtn:p.value.controls,showCenterPlayBtn:p.value.showCenterPlayBtn,showLoading:p.value.showLoading,enableProgressGesture:p.value.enableProgressGesture,objectFit:p.value.objectFit,showMuteBtn:p.value.controls,playBtnPosition:p.value.playBtnPosition,autoPauseIfNavigate:p.value.autoPauseIfNavigate,autoPauseIfOpenNative:p.value.autoPauseIfOpenNative,vslideGesture:p.value.vslideGesture,vslideGestureInFullscreen:p.value.vslideGestureInFullscreen,codec:p.value.codec,httpCache:p.value.httpCache,playStrategy:p.value.playStrategy,showProgress:p.value.showProgress,pageGesture:p.value.vslideGesture,mobilenetHintType:p.value.mobilenetHintType,enablePlayGesture:p.value.enablePlayGesture,isLive:p.value.isLive,onClick:(0,t.withModifiers)(tt,["stop"]),onPlay:Me,onPause:je,onEnded:Ve,onTimeupdate:ze,onFullscreenchange:_e,onWaiting:Qe,onError:Ke,onProgress:Ze,onLoadedmetadata:Xe,onFullscreenclick:Ye,onControlstoggle:$e,webkitPlaysinline:"true",playsinline:"true",xWebkitAirplay:"allow",x5VideoPlayerType:"h5-page",x5VideoOrientation:"portrait",style:(0,t.normalizeStyle)({width:oe.value+"px",height:J.value+"px"})},[(0,t.createElementVNode)("u-scalable",{style:{position:"absolute",left:"0",right:"0",top:"0",bottom:"0"}},[p.value.showRate||p.value.showFit?((0,t.openBlock)(),(0,t.createElementBlock)("cover-view",{key:0,class:"rateIcon",style:(0,t.normalizeStyle)({top:J.value/2+"px"})},[p.value.showRate?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:0,onClick:(0,t.withModifiers)(He,["stop"])},[(0,t.createElementVNode)("u-text",{class:"rateText"},(0,t.toDisplayString)(ye.value)+"\xD7",1)])):(0,t.createCommentVNode)("",!0),p.value.showFit?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,style:{"margin-top":"2px"},onClick:(0,t.withModifiers)(Fe,["stop"])},[(0,t.createElementVNode)("u-text",{class:"rateText",style:{"font-size":"17px","line-height":"17px"}},"\u2699")])):(0,t.createCommentVNode)("",!0)],4)):(0,t.createCommentVNode)("",!0),(0,t.createElementVNode)("cover-view",null,[(0,t.renderSlot)(f.$slots,"default")]),!E.value||x.value?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,class:"center-play-mask",style:(0,t.normalizeStyle)({width:oe.value+"px",height:fe.value+"px"})},[!x.value&&!E.value?((0,t.openBlock)(),(0,t.createElementBlock)("cover-image",{key:0,src:"data:image/png;base64,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",class:"center-play-btn",onClick:(0,t.withModifiers)(Je,["stop"])})):x.value?((0,t.openBlock)(),(0,t.createElementBlock)("cover-image",{key:1,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABX9JREFUaEPtWVuME2UU/s5MFwFFCRh4gMJ2irLMbNspm0B0o/IgoIaoGORBMQZFY6IRlPhiRMVLiAleow9GhA1GgkYMvmi8BWIIKAE63e502Ww7Xa4KSiSCLnbbOTKlXdo67XR70Uqcx3/O+c73/ef/z38j1Pj5+8NTRdG1mBmnNK+ypRhuXiIx+rT5+zoQ3QtgCMAmTVKeqTHssDvVAjQ71tthCua3AK7K4miDSfHGvra2Mzlc1dA/ALAsPw4Db4UlZWUtsXO+NQlQDX3gfI9OLyTCGzSp/SGrzRfv9YlkdtsR5RbX1LB75rFaRVQtoKM/4k2LQsyGgKFJitdqV+P6PSB8aEeSmOaHvPI31r+ZBw+OG9uSWsSC4AbSezWPb2elwqoXcHzf2PS50b8ANKYwGH2lSfJCq609FnG7BOGwDZmzoKRb8wRPB/p1hQT+EkRTcnYMbAtLypJKRDgKyPbiUgZiLoFe3d8q/5gDDhrR+xi8OS/QbybEOd1SW9/wHEhEV4H59QIyhOWaR+my2gKG3ktAWzFZBq0JS/JLTiLKCggY+lICPsqbMJGQpPjzQYMD+q1gWmFVIVMQ1nW3tiWKg6oJ/Q4wloGQBFOXJslfWzZ+Q79BAL4rQXJ4KJYTUVaAakTDABcQBmhBjoBT7zj9D8ajNzNxRozNd1yTlOFhVQrLQYB+AEAw3zl/8jkRdPo/t7//yj/FpFWJrvibLaNL8yrLnTCcBNwN4ONyQ8gpgNP/wEB0MZn8aZHdobSYDkam+3918necxAFDX0bAErtJ7ARe6f/sgrgWwGxi2vzHkPCytRh29PVdnXalFkKgKTDRo3nlz4sxHQVUSqLedrMT+i0mY2veKg8CIkMw5/dIvhN5o6LeoWvHU47oE1qG0A9ggg3aJ5qkWEM78w1n4EJJo7fB5kSQ8LwmyRtqp1IdQtDQVzOwvpR3yjSn9czwHSkQULyvyTeqjkb1XqoRfRvgR0uXTropJMmZ9SOTATXW0wmBdhU4ED2heeQ3qqdRvWe2cFi7WNsvOYhxUUU5ezEDzIKaiFobM0/WwySX6A5NaztePY3qPeft2OE63TpJA0MpRmHGC2Gv8lzpOQCeDPAaTWp/r3oKtXv6DnVLYlq09lmdF9HoHU2SH8tHb9oymiPpT+hzBZM6qEXYbjciml5ATohqROeDuZNAJ1KgXRHvrEhBFao96Y1DUI1oF8D3F0V4SpOU9U2fgUBcX0uEZ227h3BnRoA/Fp4ExqjuawJHG9ePI0duNyKTXRB+KunJfIwKdoN5J6WRh6u/R4mdauFypRq6dYJqzbaeTA7CHVWUZP3pjBwxU4EY35fxNElN6DEwMrcIAH4eT2On7fR4zo08XGM81LiugyDboTPzVgrEI3cRCdssA2Y8EPYqmxpDpTrUzK2FiB8AXF6EcDgtptWmr0IW6ayIjQDmZEQwtqfIfMQ6F/wnBJTL3f8CqhvZ9fO6tDJgXSMS6GTu0rV+/dQ4pOEMBAz9TQIet0IRaEVIkt9vXNj6IecJ6NlHoI6MAMLGkEd5sH5hGoeUL+BhAr2bCWWandoM3+7Gha0fcsEktg73gsBnDkg+21eV+oWtHGnO0d6JQylcG2qdtcfO69KqQpX3S/NYXvoZsE5FLRAXjW4Zt2WP2z3YPH1/gUnZDMi6PuqyMYgx4D7/ZldwqdosQhyHkGro1iPDeDC+0LzKbY0iblWbvVNnnRopvqOAgBFZQCxcLwrprfs9/oMjDVCpvRrveQUC9eZeLyv1cxRQKdC/ZVezgOAhXTbTeJLN1NPdMwIn/2khNQtQDf0zALcz6MWwJNteQAWO9E2hodRrZjq1ut53T7ULyDxi0yoB6ZWltiDBgd7r2DR3N2KP9RdxLfhyI6cVtQAAAABJRU5ErkJggg==",class:"center-loading"})):(0,t.createCommentVNode)("",!0),!E.value||x.value?((0,t.openBlock)(),(0,t.createElementBlock)("u-text",{key:2,style:{color:"#fff","text-align":"center"}},"\u70B9\u51FB\u64AD\u653E")):(0,t.createCommentVNode)("",!0)],4)):(0,t.createCommentVNode)("",!0)])],44,["src","poster","title","controls","autoplay","loop","muted","initialTime","duration","showFullscreenBtn","showPlayBtn","showCenterPlayBtn","showLoading","enableProgressGesture","objectFit","showMuteBtn","playBtnPosition","autoPauseIfNavigate","autoPauseIfOpenNative","vslideGesture","vslideGestureInFullscreen","codec","httpCache","playStrategy","showProgress","pageGesture","mobilenetHintType","enablePlayGesture","isLive"])):(0,t.createCommentVNode)("",!0),!we.value&&p.value.showPoster&&e.video.poster?((0,t.openBlock)(),(0,t.createElementBlock)("u-image",{key:1,src:e.video.poster,style:(0,t.normalizeStyle)({width:oe.value+"px",height:J.value+"px"})},null,12,["src"])):(0,t.createCommentVNode)("",!0)],64))}}),yt=le(mt,[["styles",[ft]]]),bt={"ml-swiper-item-view-box":{"":{position:"relative",backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item-view-right-box":{"":{position:"absolute",bottom:100,paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5,right:1,flexWrap:"wrap",flexDirection:"column"}},"ml-swiper-item-view-bottom-box":{"":{position:"absolute",bottom:1,left:0,flexWrap:"wrap",flexDirection:"column"}},"center-play-mask":{"":{position:"fixed",top:0,left:0,alignItems:"center",justifyContent:"center"}},"center-play-btn":{".center-play-mask ":{width:"140rpx",height:"140rpx"}},"center-loading":{".center-play-mask ":{width:"100rpx",height:"100rpx"}},"ml-swiper-view":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item-video":{"":{backgroundColor:"rgba(0,0,0,0.7)"}}},wt=Object.assign({name:"ml-swiper"},{__name:"ml-swiper",props:{width:{type:Number,default:0,required:!1},height:{type:Number,default:0,required:!1},rightStyle:{type:[Object,String],default:{},required:!1},bottomStyle:{type:[Object,String],default:{},required:!1},videoList:{type:Array,default:[],required:!0},count:{type:Number,default:2,required:!1},showPlay:{type:Boolean,default:!0,required:!1},hidePause:{type:Boolean,default:!0,required:!1}},emits:["change","play","pause","ended","error","waiting","videoClick","doubleClick","loadMore","maskClick"],setup(e,{emit:i}){let n=uni.getSystemInfoSync(),u=e,m=(0,t.ref)({width:u.width||n.windowWidth,height:u.height||n.windowHeight,controls:!1,initialTime:0,vslideGesture:!1,showRate:!1,showFit:!1,enableClick:!0,enableDblClick:!0,autoplay:!0,showPoster:!0,loop:!0,muted:!1,objectFit:"contain"}),r=null,v=(0,t.ref)(0),y=(0,t.computed)(()=>u.videoList||[]),h=(0,t.ref)(!1),w=(0,t.ref)(!1),P=(0,t.ref)(!0),b=(0,t.computed)(()=>({width:m.value.width+"px",height:m.value.height+"px"})),T=i,C=l=>{if(h.value)return;h.value=!0,setTimeout(()=>{h.value=!1},500);let s=l.detail.current;I(),v.value=s,R()},R=()=>{z();let l=y.value.length;T("change",v.value,l)},z=()=>{let l=y.value.length,s=Number(isNaN(u.count)?2:u.count);Number(Number(l)-s)==v.value&&T("loadMore",v.value,l)},B=0,_=l=>{if(Date.now()-B<200){W(r,y.value[v.value]),B=0;return}B=Date.now()},Q=l=>{l&&(r=l),w.value=!0,P.value=!1,T("play",r)},F=l=>{l&&(r=l),w.value=!1,T("pause",r)},H=l=>{l&&(r=l),P.value=!0,T("waiting",r)},K=l=>{l&&(r=l),T("ended",r)},Z=(l,s)=>{l&&(r=l),P.value=!1,T("error",r,s)},X=(l,s)=>{l&&(r=l),w.value===!0?I():U(),T("videoClick",r,s)},W=(l,s)=>{l&&(r=l),T("doubleClick",r,s)};function I(){if(r)try{r==null||r.pause(),w.value=!1}catch(l){A("error","at components/ml-swiper/ml-swiper.vue:366",l)}}function U(){if(r){A("log","at components/ml-swiper/ml-swiper.vue:373",r);try{r==null||r.play(),w.value=!0,P.value=!1}catch(l){A("error","at components/ml-swiper/ml-swiper.vue:379",l)}}}function S(){if(r)try{r==null||r.stop()}catch(l){A("error","at components/ml-swiper/ml-swiper.vue:389",l)}}return pe(()=>{u.showPlay&&U()}),(0,t.onMounted)(()=>{}),Re(()=>{u.hidePause&&I()}),Ue(()=>{B=0,S(),v.value=0,h.value=!1,w.value=!1,P.value=!0,r=null}),(l,s)=>{let k=ce((0,t.resolveDynamicComponent)("ml-player"),yt),q=(0,t.resolveComponent)("swiper-item"),M=(0,t.resolveComponent)("swiper");return(0,t.openBlock)(),(0,t.createElementBlock)("view",{class:"ml-swiper-view",renderWhole:!0},[y.value&&y.value.length>0?((0,t.openBlock)(),(0,t.createBlock)(M,{key:0,class:"ml-swiper",current:v.value,circular:!1,vertical:!0,"skip-hidden-item-layout":!0,style:(0,t.normalizeStyle)(b.value),onTouchend:_,onChange:C},{default:(0,t.withCtx)(()=>[((0,t.openBlock)(!0),(0,t.createElementBlock)(t.Fragment,null,(0,t.renderList)(y.value,(j,V)=>((0,t.openBlock)(),(0,t.createBlock)(q,{class:"ml-swiper-item",key:V},{default:(0,t.withCtx)(()=>[(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-box",style:(0,t.normalizeStyle)(b.value)},[(0,t.createVNode)(k,{showPlayer:v.value===V,video:j,videoOptions:m.value,onVideoClick:X,onDoubleClick:W,onPlay:Q,onPause:F,onWaiting:H,onEnded:K,onError:Z},null,8,["showPlayer","video","videoOptions"]),(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-right-box",style:(0,t.normalizeStyle)(e.rightStyle)},[(0,t.renderSlot)(l.$slots,"right",{video:j,index:V})],4),(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-bottom-box",style:(0,t.normalizeStyle)(e.bottomStyle)},[(0,t.renderSlot)(l.$slots,"bottom",{video:j,index:V})],4)],4)]),_:2},1024))),128))]),_:3},8,["current","style"])):(0,t.createCommentVNode)("",!0)])}}}),Ee=le(wt,[["styles",[bt]]]),Ct=(e,i,n)=>new Promise((u,m)=>{let r=!1,{method:v="POST",header:y,isLoad:h=!0}=n;h&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),r=!0);let w=e;uni.request({url:w,method:v,data:i,header:y,success:P=>{let{data:b}=P;switch(r&&uni.hideLoading(),b.code){case 0:u(b);break;default:b.msg&&!b.msg.includes("\u67E5\u8BE2\u6570\u636E\u4E0D\u5B58\u5728")&&uni.showToast({title:b.msg||"\u7F51\u7EDC\u51FA\u9519",icon:"none"}),b.data.reload&&(uni.clearStorageSync(),uni.reLaunch({url:"/pagesSub/pages/login/login"})),m(b.msg||"\u7F51\u7EDC\u51FA\u9519");break}},complete:P=>{r&&uni.hideLoading(),A("log","at apis/https.js:53",w,i,P)}})}),At=(e,i,n)=>new Promise((u,m)=>{let{method:r="POST",header:v,isLoad:y=!0}=n,h=!1;y&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),h=!0);let w=e;uni.request({url:w,method:r,data:i,header:v,success:P=>{let{data:b}=P;switch(h&&uni.hideLoading(),b.ErrCode){case"0":u(b);break;default:b.ErrCode!=="60001"&&uni.showToast({title:b.ErrMsg||"\u7F51\u7EDC\u51FA\u9519",icon:"none"}),u(b);break}},complete:P=>{h&&uni.hideLoading(),A("log","at apis/httpsNew.js:53",w,i,P)}})}),ee=(0,Be.defineStore)("userInfo",()=>{let e=(0,t.ref)(""),i=(0,t.ref)({}),n=(0,t.ref)(0),u=(0,t.ref)(""),m=(0,t.ref)(0),r=(0,t.ref)(""),v=(0,t.ref)(""),y=(0,t.ref)(0),h=(0,t.ref)(0);function w(T){let C=T.user.roles[0];i.value=T.user,e.value=T.token,C!=null&&(n.value=C.teamID,u.value=C.teamName,m.value=C.companyID,r.value=C.companyName,v.value=C.roleName,y.value=C.role_ID,h.value=C.beanRate)}function P(T,C){switch(T){case"token":e.value=C;break;case"user":i.value=C;break;case"teamID":n.value=C;break;case"teamName":u.value=C;break;case"companyID":m.value=C;break;case"companyName":r.value=C;break;case"roleName":v.value=C;break;case"roleID":y.value=C;break;case"beanRate":h.value=C;break}}function b(){e.value="",i.value={},n.value=0,u.value="",m.value=0,r.value="",v.value="",y.value=0,h.value=0}return{token:e,user:i,teamID:n,teamName:u,companyID:m,companyName:r,roleName:v,roleID:y,beanRate:h,setInfo:w,setItem:P,clear:b}},{unistorage:!0}),Pt=(e,i,n)=>new Promise((u,m)=>{let{method:r="POST",header:v,isLoad:y=!0}=n,h=!1;y&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),h=!0);let w=e;uni.request({url:w,method:r,data:i,header:v,success:P=>{let{data:b}=P;switch(h&&uni.hideLoading(),b.ErrCode){case"0":u(b);break;default:u(b);break}},complete:P=>{h&&uni.hideLoading(),A("log","at apis/httpsNewWithoutNotion.js:50",w,i,P)}})}),te={AppUser:{Login:e=>o("appUser/login",e),Register:e=>o("appUser/register",e),CheckExist:e=>o("appUser/checkExist",e,"GET"),UserList:e=>o("appUser/getUserList",e,"GET"),CheckUserStatus:e=>a("Clothing/User/CheckUserStatus",e,"POST"),AddLoginLog:e=>a("Clothing/Staff/AddLoginLog",e,"POST"),ModifyPwd:e=>a("Clothing/Staff/ModifyPwd",e,"POST"),AddMember:e=>a("Clothing/User/AddMember",e,"POST"),CheckUserAuth:e=>a("Clothing/User/CheckUserAuth",e,"POST"),CheckCheckMemberStatus:e=>a("Clothing/User/CheckCheckMemberStatus",e,"POST")},AppRole:{List:()=>o("appRole/getAppRoleList",null,"GET")},Company:{List:()=>o("company/getCompanyList",null,"GET"),Join:e=>o("company/joinCompany",e),Check:e=>o("companyApply/optApply",e,"PUT"),Apply:e=>o("companyApply/findCompanyApply",e,"GET"),DelUser:e=>o("company/deleteStaff",e,"DELETE")},Team:{List:e=>o("team/getTeamList",e,"GET"),Join:e=>o("team/joinTeam",e),ApplyDetail:e=>o("teamApply/findTeamApply",e,"GET"),Check:e=>o("teamApply/optApply",e,"PUT"),DelUser:e=>o("team/deleteMember",e,"DELETE")},Staff:{GroupList:e=>a("Clothing/Staff/GroupList",e,"POST"),UserList:e=>a("Clothing/Staff/UserList",e,"POST"),UserListEnhanced:e=>a("Clothing/Staff/getUserListEnhanced",e,"POST"),GetCompanyList:()=>a("Clothing/Staff/GetCompanyList",null,"POST"),RoleList:()=>a("Clothing/Staff/RoleList",null,"POST"),AddGroup:e=>a("Clothing/Staff/AddGroup",e,"POST"),AddTeam:e=>a("Clothing/Staff/AddTeam",e,"POST"),EditTeam:e=>a("Clothing/Staff/EditTeam",e,"POST"),DeleteTeam:e=>a("Clothing/Staff/DeleteTeam",e,"POST"),UpdateGroup:e=>a("Clothing/Staff/UpdateGroup",e,"POST"),DeleteGroup:e=>a("Clothing/Staff/DeleteGroup",e,"POST"),ExistCompany:e=>a("Clothing/Staff/ExistCompany",e,"POST"),AddCompany:e=>a("Clothing/Staff/AddCompany",e,"POST"),UserRoleList:e=>a("Clothing/Staff/UserRoleList",e,"POST"),UserListByComp:e=>G("Clothing/Staff/UserListByComp",e,"POST")},CroppingRecord:{List:e=>o("croppingRecord/getCroppingRecordList",e,"GET"),Detail:e=>o("croppingRecord/findCroppingRecord",e,"GET"),Update:e=>o("croppingRecord/updateCroppingRecord",e,"PUT"),Add:e=>o("croppingRecord/createCroppingRecord",e),Delete:e=>o("croppingRecord/deleteCroppingRecord",e,"DELETE"),AddNew:e=>o("Clothing/CroppingRecord/createCroppingRecord",e,"POST"),UpdatePrintStatus:e=>a("Clothing/CroppingRecord/UpdatePrintStatus",e,"POST"),GetPrintStatus:e=>a("Clothing/CroppingRecord/GetPrintStatus",e,"POST")},Style:{List:e=>o("style/getStyleList",e,"GET"),Add:e=>o("style/createStyle",e),Detail:e=>o("style/findStyle",e,"GET"),Update:e=>o("style/updateStyle",e,"PUT"),DeleteStyle:e=>a("Clothing/Process/DeleteStyle",e,"POST"),StyleList:e=>a("Clothing/CroppingRecord/StyleList",e,"POST")},Cloth:{List:e=>o("cloth/getClothList",e,"GET"),Add:e=>o("cloth/creatCloth",e),Detail:e=>o("cloth/findCloth",e,"GET"),Update:e=>o("cloth/updateCloth",e,"PUT"),AddCloth:e=>a("Clothing/Cloth/AddCloth",e,"POST"),UpdateCloth:e=>a("Clothing/Cloth/UpdateCloth",e,"POST"),ClothListByPage:e=>a("Clothing/Cloth/ClothListByPage",e,"POST")},Wallet:{List:e=>o("userWallet/getUserWalletList",e,"GET"),My:()=>o("userWallet/getMyWalletList",null,"GET"),QueryWallet:e=>a("Clothing/UserWallet/QueryWallet",e,"POST"),ExportWallet:e=>a("Clothing/UserWallet/Export2",e,"POST"),WageSettle:e=>a("Clothing/UserWallet/WageSettle",e,"POST"),WageSettleCancel:e=>a("Clothing/UserWallet/WageSettleCancel",e,"POST")},Job:{List:e=>o("jobQuestion/getJobQuestionList",e,"GET"),Detail:e=>o("job/findJob",e,"GET"),Question:e=>o("jobQuestion/findJobQuestion",e,"GET"),Update:e=>o("jobQuestion/handleJobQuestion",e,"PUT"),Add:e=>o("jobQuestion/createJobQuestion",e),Job:e=>o("job/getJobList",e,"GET"),Process:e=>o("job/getJobGroupByProcess",e,"GET"),ToApply:e=>o("job/jobAuditApply",e,"PUT"),Check:e=>o("job/jobAuditOpt",e,"PUT"),ChangeWorker:e=>o("job/changeWorker",e,"PUT"),Task:e=>o("job/postJobList",e),Wages:e=>o("job/getWagesDetail",e,"GET"),Apply:{Detail:e=>o("jobApply/findJobApply",e,"GET"),Add:e=>o("jobApply/createJobApply",e),Check:e=>o("jobApply/optApply",e,"PUT")},JobReceiveList:e=>a("Clothing/Job/JobReceiveList",e,"POST"),JobReceiveDetail:e=>a("Clothing/Job/JobReceiveDetail",e,"POST"),UpdateJobStatus:e=>a("Clothing/Job/UpdateJobStatus",e,"POST"),DeleteJob:e=>G("Clothing/Job/DeleteJob",e,"POST"),AddJob:e=>a("Clothing/Job/AddJob",e,"POST"),AddJobForOther:e=>a("Clothing/Job/AddJobForOther",e,"POST"),AddJobAll:e=>a("Clothing/Job/AddJobAll",e,"POST"),JobFinishList:e=>G("Clothing/Job/JobFinishList",e,"POST"),JobReceiveListByCroppingRecord:e=>G("Clothing/Job/JobReceiveListByCroppingRecord",e,"POST"),UpdateJobPrice:e=>a("Clothing/Job/UpdateJobPrice",e,"POST")},Inventory:{Stock:e=>o("inventory/getInventoryList",e,"GET")},Process:{List:e=>o("process/getProcessList",e,"GET"),ListNew:e=>a("Clothing/Process/ProcessListNew",e,"POST"),Detail:e=>o("process/findProcess",e,"GET"),Add:e=>o("process/createProcess",e),Update:e=>o("process/updateProcess",e,"PUT"),UpdateProcess:e=>a("Clothing/Process/UpdateProcess",e,"POST"),AddProcess:e=>a("Clothing/Process/AddProcess",e,"POST"),ProcessList:e=>a("Clothing/Process/ProcessList",e,"POST"),ProcessListByPage:e=>a("Clothing/Process/ProcessListByPage",e,"POST"),GetSizeQuanlity:e=>a("Clothing/Process/GetSizeQuanlity",e,"POST"),GetList:e=>a("Clothing/Process/StyleList",e,"POST"),GetStandard:e=>a("Clothing/Process/GetStandard",e,"POST"),AddStandard:e=>G("Clothing/Process/AddStandard",e,"POST"),GetMyStandard:e=>G("Clothing/Process/GetMyStandard",e,"POST"),StyleSettle:e=>a("Clothing/Process/StyleSettle",e,"POST"),StyleSettleCancel:e=>a("Clothing/Process/StyleSettleCancel",e,"POST")},Chat:{GroupList:e=>a("IM/Group/GroupList",e,"POST"),AddGroup:e=>G("IM/Group/AddGroup",e,"POST"),CreateGroup:e=>a("IM/Group/CreateGroup",e,"POST"),GetUser:e=>a("IM/Chat/GetUser",e,"POST")},Message:{List:e=>o("msgBox/getMyMsgBoxList",e,"GET"),Send:e=>o("msgBox/getMySendMsgList",e,"GET"),Read:e=>o("msgBox/setRead",e,"GET")},Banner:{List:()=>o("banner/getBannerList",null,"GET"),Find:e=>o("banner/findBanner",e,"GET")},Computation:{Do:e=>o("computation/doComputation",e)},Order:{List:e=>o("order/getOrderList",e,"GET"),Detail:e=>o("order/findOrder",e,"GET"),Goods:()=>o("rechargeOption/getRechargeOptionList",null,"GET"),Add:e=>o("order/createOrder",e),Pay:e=>o("order/payOrder",e)},Common:{Request:(e,i,n)=>o(e,i,n),Request1:(e,i,n)=>a(e,i,n),GetInfo:e=>a("Clothing/Common/GetInfo",e,"POST"),AddColors:e=>a("Clothing/Common/AddColors",e,"POST"),GetColors:e=>a("Clothing/Common/GetColors",e,"POST"),Today:e=>a("Clothing/Date/Today",e,"GET"),Now:e=>a("Clothing/Date/Now",e,"GET")},Question:{QuestionList:e=>a("Clothing/Job/QuestionList",e,"POST"),ReplyQuestion:e=>a("Clothing/Job/ReplyQuestion",e,"POST")},Adv:{AdvList:e=>G("ADV/AdvInfo/AdvList",e,"POST"),AddAdv:e=>G("ADV/AdvInfo/AddAdv",e,"POST"),DeleteAdv:e=>a("ADV/AdvInfo/DeleteAdv",e,"POST"),PayAdv:e=>a("ADV/AdvInfo/PayAdv",e,"POST"),BigList:e=>G("ADV/AdvInfo/BigList",e,"POST"),SmallList:e=>G("ADV/AdvInfo/SmallList",e,"POST"),BuyList:e=>a("ADV/AdvInfo/BuyList",e,"POST"),AdvDetial:e=>a("ADV/AdvInfo/AdvDetail",e,"POST")},Url:{baseUrl:"http://8.138.8.6:8889",baseUrlBiz:"http://8.138.8.6:8890",updateUrl:"https://wxapi.ruiruicaikuai.com/",baseUrlFileUpload:"https://wxapi.ruiruicaikuai.com/api/Files/Upload"},App:{vuex_version:"1.3.7",agent_code:""}},St="http://8.138.8.6:8889",Ne="http://8.138.8.6:8890",o=(e,i,n="POST")=>{let{token:u}=ee(),m=St+"/api/"+e;return Ct(m,i,{header:{"x-token":u},method:n})},a=(e,i,n="POST")=>{ee();let u=Ne+"/api/"+e;return At(u,i,{header:{},method:n})},G=(e,i,n="POST")=>{ee();let u=Ne+"/api/"+e;return Pt(u,i,{header:{},method:n})};var c=ie(ue()),We=ie(de());var Tt={imgV:{"":{width:100,textAlign:"center",minHeight:65}},work:{"":{backgroundColor:"#ffffff",marginTop:"10rpx",marginRight:"10rpx",marginBottom:"10rpx",marginLeft:"10rpx",borderRadius:6}},title:{".work ":{display:"flex",alignItems:"center",height:"80rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",borderBottomWidth:1,borderBottomStyle:"solid",borderBottomColor:"#e5e7eb"}},menu:{".work ":{gridTemplateColumns:"repeat(3, minmax(0, 1fr))",gap:"30rpx",paddingTop:"30rpx",paddingRight:0,paddingBottom:"30rpx",paddingLeft:0,fontSize:"24rpx",textAlign:"center"}},item:{".work .menu ":{color:"#999999"},".work .menu_1 ":{color:"#999999",textAlign:"center"},".search ":{display:"flex",alignItems:"center",gap:"20rpx",fontSize:"28rpx"}},img:{".work .menu .item ":{height:"128rpx",width:"128rpx",borderRadius:6,marginTop:0,marginBottom:"10rpx"},".work .menu_1 .item ":{height:"100rpx",width:"100rpx",borderRadius:6,marginTop:"10rpx",marginRight:"10rpx",marginBottom:"20rpx",marginLeft:"10rpx"}},menu_1:{".work ":{gridTemplateColumns:"repeat(4, minmax(0, 1fr))",gap:"30rpx",paddingTop:"30rpx",paddingRight:0,paddingBottom:"30rpx",paddingLeft:0,fontSize:"24rpx",textAlign:"center"}},search:{"":{gridTemplateColumns:"repeat(1, minmax(0, 1fr))",paddingTop:"20rpx",paddingRight:"20rpx",paddingBottom:"20rpx",paddingLeft:"20rpx",gap:"20rpx",backgroundColor:"#ffffff"}},label:{".search .item ":{flexShrink:0}},input:{".search .item ":{flex:1,backgroundColor:"#f8f8f8",borderRadius:"10rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",minHeight:"60rpx"}},value:{".search .item .input ":{display:"flex",alignItems:"center",justifyContent:"space-between",minHeight:"60rpx","fontFamily::after":'"iconfont"',"content::after":'"\\e840"',"color::after":"#999999","content:empty::before":'"\u8BF7\u9009\u62E9"',"color:empty::before":"#808080"}},submit:{".search ":{backgroundColor:"#007aff",color:"#ffffff"}},btnGroup:{".search ":{"!width":"100rpx"}},submit_deploy:{"":{position:"fixed",left:"20rpx",right:"20rpx",backgroundColor:"#007aff",color:"#ffffff",height:"80rpx",bottom:"10rpx"}},"person-head":{"":{position:"relative",backgroundColor:"#ffffff",marginLeft:"20rpx",marginRight:"20rpx"}},"uniui-right":{"":{justifyContent:"center"}},videoTitle:{"":{paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5,color:"#de4a00",fontSize:13,lines:13,whiteSpace:"normal"}},userAvatar:{"":{width:35,height:35,borderRadius:100,marginBottom:10,borderWidth:"1rpx",borderStyle:"solid",borderColor:"#ffffff",backgroundColor:"#fafafa"}},iconTitle:{"":{fontSize:12,color:"#ffffff",textAlign:"center",paddingBottom:5}}},Ot={__name:"advDetail",setup(e){let i=ee(),{user:n,roleID:u,teamID:m}=(0,We.storeToRefs)(i),r=(0,c.ref)(0);(0,c.ref)([]);let v=(0,c.ref)([]);(0,c.ref)([]),(0,c.ref)(""),(0,c.ref)([]);let y="",h=uni.getSystemInfoSync(),w=h.windowWidth,P=h.windowHeight,b={top:"1px"},T=(0,c.ref)([]),C=(0,c.ref)(0),R=(0,c.ref)(0),z=S=>{A("log","at pagesSub/pages/adv/advDetail.nvue:108","onchange-\u5F53\u524D\u7D22\u5F15:",S),C.value=S},B=(S,l)=>{A("log","at pagesSub/pages/adv/advDetail.nvue:114","\u52A0\u8F7D\u66F4\u6240\u89C6\u9891\uFF1A",S+" / "+l),!(R.value>5)&&(I().forEach(s=>{s.title=T.value.length+"\uFF0C"+s.title+s.title+s.title,T.value.push(s)}),R.value=R.value+1)},_=S=>{A("log","at pagesSub/pages/adv/advDetail.nvue:128","\u89C6\u9891\u5F00\u59CB\u64AD\u653E")},Q=S=>{A("log","at pagesSub/pages/adv/advDetail.nvue:135","\u89C6\u9891\u6682\u505C\u64AD\u653E")},F=S=>{A("log","at pagesSub/pages/adv/advDetail.nvue:142","\u89C6\u9891\u64AD\u653E\u7ED3\u675F")},H=(S,l)=>{A("error","at pagesSub/pages/adv/advDetail.nvue:149","\u89C6\u9891\u64AD\u653E\u51FA\u9519\uFF1A",l)},K=S=>{A("error","at pagesSub/pages/adv/advDetail.nvue:156","\u89C6\u9891\u51FA\u73B0\u7F13\u51B2")},Z=(S,l)=>{A("log","at pagesSub/pages/adv/advDetail.nvue:163","\u70B9\u51FB\u4E86\u89C6\u9891\uFF1A",l)},X=(S,l)=>{A("log","at pagesSub/pages/adv/advDetail.nvue:170","\u53CC\u51FB\u4E86\u89C6\u9891\uFF1A",l)},W=(S,l)=>{A("log","at pagesSub/pages/adv/advDetail.nvue:177","\u70B9\u51FB\u4E86\u8499\u5C42\uFF1A",S,l)},I=()=>[{videoId:T.value.length+1,title:"\u3002",poster:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",url:"https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",uploadTime:"2023-11-08 19:41",ipLocation:"\u4E0A\u6D77",author:{authorId:101,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u964C\u8DEF",genderName:"\u7537"}}];pe(()=>{(0,c.toRaw)(n.value).ID==""&&(i.clear(),uni.reLaunch({url:"/pagesSub/pages/login/login"})),u.value==""&&uni.reLaunch({url:"/pages/my/my"})}),Ie(S=>{y=S.aid,U()}),qe(()=>{uni.navigateTo({url:"/pagesSub/pages/message/message"})}),(0,c.onMounted)(()=>{U()});let U=()=>xe(this,null,function*(){A("log","at pagesSub/pages/adv/advDetail.nvue:282",y),v.value.splice(0);let S=(0,c.toRaw)(n.value);A("log","at pagesSub/pages/adv/advDetail.nvue:285",S.ID),te.Adv.AdvDetial({id:y.toString()}).then(function(l){if(A("log","at pagesSub/pages/adv/advDetail.nvue:289",l),l.Success&&l.ResData.length>0){var s=l.ResData[0];if(s.category=="big",s.category=="small",v.value.length==0&&(s.type=="SH"&&v.value.push({videoId:v.value.length+1,title:`\u63A5\u8D27\u6D88\u606F:
	\u5730\u5740:`+s.sh_address+`
	\u59D3\u540D:`+s.sh_name+`
	\u8054\u7CFB\u7535\u8BDD:`+s.sh_tel+`
	\u4EBA\u6570\u89C4\u6A21:`+s.sh_gm+`\u4EBA 
	\u64C5\u957F\u6B3E\u5F0F:`+s.sh_style+`
	\u5305\u6599\u80FD\u529B:`+s.sh_blnl+`
	\u6253\u7248\u5F00\u53D1\u80FD\u529B:`+s.sh_dbkfnl+`
	\u52A0\u5DE5\u96BE\u5EA6:`+s.sh_jgnd+`
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:te.Url.baseUrlBiz+"/"+s.sh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973"}}),s.type=="FH"&&v.value.push({videoId:v.value.length+1,title:`\u53D1\u8D27\u6D88\u606F:
	\u8054\u7CFB\u7535\u8BDD:`+s.fh_Tel+`
	\u671F\u671B\u52A0\u5DE5\u5730\u5740:`+s.fh_address+`
	\u5DE5\u671F\u9650\u5236:`+s.fh_limitGQ+`
	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:`+s.fh_fzType+`
	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:`+s.fh_JBGYBZ+`
	\u8D26\u671F\u671F\u671B:`+s.fh_ZQQW+`
	\u8BA2\u5355\u6570\u91CF:`+s.fh_orderNum+`
	\u662F\u5426\u5305\u88C1:`+s.fh_SFBC+`
	\u662F\u5426\u5305\u9762\u8F85\u6599:`+s.fh_SFMLFZ+`
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:te.Url.baseUrlBiz+"/"+s.fh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973"}}),s.type=="ZG")){var k=JSON.parse(s.zg_zaopinList),q="";k.forEach(function(M,j){q+=M.type+"  \u62DB\u8058\u4EBA\u6570:"+M.quantity+`\u4EBA
	`}),A("log","at pagesSub/pages/adv/advDetail.nvue:353",k),v.value.push({videoId:v.value.length+1,title:`\u62DB\u8058\u901A\u77E5:
	\u5730\u5740:`+s.zg_address+`
	\u59D3\u540D:`+s.zg_name+`
	\u8054\u7CFB\u7535\u8BDD:`+s.zg_tel+`
	\u62DB\u8058\u4FE1\u606F:
	`+q,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:te.Url.baseUrlBiz+"/"+s.zg_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973"}})}}A("log","at pagesSub/pages/adv/advDetail.nvue:374",v.value)}.bind(this))});return(S,l)=>{let s=ce((0,c.resolveDynamicComponent)("ml-swiper"),Ee);return(0,c.openBlock)(),(0,c.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[r.value==0?((0,c.openBlock)(),(0,c.createElementBlock)("view",{key:0,class:"work_1"},[(0,c.createElementVNode)("view",{class:"vedioContent"},[(0,c.createVNode)(s,{videoList:(0,c.unref)(v),width:(0,c.unref)(w),height:(0,c.unref)(P),bottomStyle:b,onLoadMore:B,onChange:z,onPlay:_,onPause:Q,onEnded:F,onError:H,onWaiting:K,onVideoClick:Z,onDoubleClick:X,onMaskClick:W},{bottom:(0,c.withCtx)(({video:k,index:q})=>[k?((0,c.openBlock)(),(0,c.createElementBlock)("u-text",{key:0,class:"videoTitle"},(0,c.toDisplayString)(k==null?void 0:k.title),1)):(0,c.createCommentVNode)("",!0)]),_:1},8,["videoList","width","height"])])])):(0,c.createCommentVNode)("",!0)])}}},ne=le(Ot,[["styles",[Tt]]]);var ge=plus.webview.currentWebview();if(ge){let e=parseInt(ge.id),i="pagesSub/pages/adv/advDetail",n={};try{n=JSON.parse(ge.__query__)}catch(m){}ne.mpType="page";let u=Vue.createPageApp(ne,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:i,__pageQuery:n});u.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...ne.styles||[]])),u.mount("#root")}})();
