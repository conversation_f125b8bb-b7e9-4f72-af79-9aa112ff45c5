{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__D1E0532", "name": "睿睿", "version": {"name": "1.3.8", "code": "100"}, "description": "", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Bluetooth": {}, "Barcode": {}, "Camera": {}, "Contacts": {}, "FaceID": {}, "Fingerprint": {}, "Geolocation": {}, "VideoPlayer": {}, "SQLite": {}, "Record": {}, "UIWebview": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": true, "delay": 0, "target": "id:1", "waiting": true}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"icons": {"android": {"hdpi": "icon-android-hdpi.png", "xhdpi": "icon-android-xhdpi.png", "xxhdpi": "icon-android-xxhdpi.png", "xxxhdpi": "icon-android-xxxhdpi.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}, "prerendered": "false"}}, "splashscreen": {"useOriginalMsgbox": true, "androidStyle": "default", "android": {"hdpi": "splash-android-hdpi.png", "xxhdpi": "splash-android-xxhdpi.png", "xhdpi": "splash-android-xhdpi.png"}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "packagename": "uni.app.UNID1E0532", "aliasname": "rui<PERSON>i", "password": "ep/Tdjka4Y7WYqDB6/S7dw==", "storepwd": "ep/Tdjka4Y7WYqDB6/S7dw==", "keypwd": "ep/Tdjka4Y7WYqDB6/S7dw==", "keystore": "google-keystore.keystore", "custompermissions": true}, "apple": {"dSYMs": false, "devices": "universal"}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}, "geolocation": {"system": {"__platform__": ["ios", "android"]}}}, "orientation": "portrait-primary"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.75", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#999", "selectedColor": "#007aff", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "50px", "list": [{"iconPath": "/static/tabbar/home.png", "selectedIconPath": "/static/tabbar/home_on.png", "pagePath": "pages/index/index", "text": "首页"}, {"iconPath": "/static/tabbar/my.png", "selectedIconPath": "/static/tabbar/my_on.png", "pagePath": "pages/my/my", "text": "我的"}], "selectedIndex": 0, "shown": true, "child": ["lauchwebview"], "selected": 0}, "adid": "121800170406"}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}}, "launch_path": "__uniappview.html"}