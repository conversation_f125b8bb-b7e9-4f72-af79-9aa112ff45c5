"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(() => {
  var __create = Object.create;
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getProtoOf = Object.getPrototypeOf;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __commonJS = (cb, mod) => function __require() {
    return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
    // If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
    mod
  ));
  var __async = (__this, __arguments, generator) => {
    return new Promise((resolve, reject) => {
      var fulfilled = (value) => {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      };
      var rejected = (value) => {
        try {
          step(generator.throw(value));
        } catch (e) {
          reject(e);
        }
      };
      var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
      step((generator = generator.apply(__this, __arguments)).next());
    });
  };

  // vue-ns:vue
  var require_vue = __commonJS({
    "vue-ns:vue"(exports, module) {
      module.exports = Vue;
    }
  });

  // pinia-ns:pinia
  var require_pinia = __commonJS({
    "pinia-ns:pinia"(exports, module) {
      module.exports = uni.Pinia;
    }
  });

  // ../../../../睿睿源码/App/unpackage/dist/dev/.nvue/index.js
  var import_vue = __toESM(require_vue());
  var import_pinia = __toESM(require_pinia());
  var ON_SHOW = "onShow";
  var ON_HIDE = "onHide";
  var ON_LOAD = "onLoad";
  var ON_READY = "onReady";
  var ON_UNLOAD = "onUnload";
  var ON_NAVIGATION_BAR_BUTTON_TAP = "onNavigationBarButtonTap";
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  function resolveEasycom(component, easycom) {
    return typeof component === "string" ? easycom : component;
  }
  var createLifeCycleHook = (lifecycle, flag = 0) => (hook, target = (0, import_vue.getCurrentInstance)()) => {
    !import_vue.isInSSRComponentSetup && (0, import_vue.injectHook)(lifecycle, hook, target);
  };
  var onShow = /* @__PURE__ */ createLifeCycleHook(
    ON_SHOW,
    1 | 2
    /* HookFlags.PAGE */
  );
  var onHide = /* @__PURE__ */ createLifeCycleHook(
    ON_HIDE,
    1 | 2
    /* HookFlags.PAGE */
  );
  var onLoad = /* @__PURE__ */ createLifeCycleHook(
    ON_LOAD,
    2
    /* HookFlags.PAGE */
  );
  var onReady = /* @__PURE__ */ createLifeCycleHook(
    ON_READY,
    2
    /* HookFlags.PAGE */
  );
  var onUnload = /* @__PURE__ */ createLifeCycleHook(
    ON_UNLOAD,
    2
    /* HookFlags.PAGE */
  );
  var onNavigationBarButtonTap = /* @__PURE__ */ createLifeCycleHook(
    ON_NAVIGATION_BAR_BUTTON_TAP,
    2
    /* HookFlags.PAGE */
  );
  var _style_0$1 = { "ml-player": { "": { "position": "relative", "zIndex": 0 } }, "rateIcon": { ".ml-player ": { "position": "absolute", "right": 3, "top": 95, "zIndex": 1 } }, "rateText": { ".ml-player .rateIcon ": { "fontSize": 11, "color": "#ffffff", "backgroundColor": "rgba(0,0,0,0.5)", "paddingTop": "5rpx", "paddingRight": "8rpx", "paddingBottom": "5rpx", "paddingLeft": "8rpx", "borderRadius": "8rpx", "height": 22, "lineHeight": 22 } }, "center-play-mask": { ".ml-player ": { "position": "fixed", "top": 0, "left": 0, "alignItems": "center", "justifyContent": "center", "backgroundColor": "rgba(0,0,0,0.2)" } }, "center-play-btn": { ".ml-player .center-play-mask ": { "width": "140rpx", "height": "140rpx" } }, "center-loading": { ".ml-player .center-play-mask ": { "width": "100rpx", "height": "100rpx" } } };
  var _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  var _sfc_main$1 = /* @__PURE__ */ Object.assign({ name: "MlPlayer" }, {
    __name: "ml-player",
    props: {
      // 显示播放器
      showPlayer: {
        type: Boolean,
        default: true
      },
      // 资源配置
      video: {
        // {url:"", poster:"", title:""}
        type: Object,
        default: {},
        required: true
      },
      // 弹幕配置
      danmu: {
        danmuList: {
          // 弹幕信息列表 { text: '第1s出现的弹幕，红色字体', color: '#ff0000', time: 1}
          type: Array,
          default: []
        },
        danmuBtn: {
          // 是否显示弹幕按钮，只在初始化时有效，不能动态变更
          type: Boolean,
          default: false
        },
        enableDanmu: {
          // 是否展示弹幕，只在初始化时有效，不能动态变更
          type: Boolean,
          default: false
        }
      },
      // 播放器插件配置
      videoOptions: {
        type: Object,
        default: {
          width: 0,
          // 组件宽度
          height: 0,
          // 组件高度
          fillHeight: false,
          // 是否填充高度
          controls: true,
          // 是否显示默认播放控件（播放/暂停按钮、播放进度、时间） 
          autoplay: true,
          // 是否自动播放
          loop: true,
          // 是否循环播放
          muted: false,
          // 是否静音播放
          initialTime: 0,
          // 指定视频初始播放位置 单位为秒（s）
          duration: 0,
          // 指定视频长度，单位为秒（s）
          showPoster: true,
          // 显示预览图
          showProgress: true,
          // 显示进度条
          // showFullscreenBtn: true, // 是否显示全屏按钮
          // showPlayBtn: false, // 是否显示视频底部控制栏的播放按钮
          showCenterPlayBtn: true,
          // 是否显示视频中间的播放按钮
          enablePlayGesture: false,
          // 是否开启播放手势，即双击切换播放/暂停
          showLoading: true,
          // 是否显示loading控件
          enableProgressGesture: true,
          // 是否开启控制进度的手势
          objectFit: "contain",
          // 当视频大小与 video 容器大小不一致时，视频的表现形式。contain：包含、fill：填充、cover：覆盖
          // showMuteBtn: false, // 是否显示静音按钮
          playBtnPosition: "center",
          // 播放按钮的位置：bottom、center
          mobilenetHintType: 1,
          // 移动网络提醒样式：0是不提醒，1是提醒，默认值为1
          autoPauseIfNavigate: true,
          // 微信：当跳转到其它小程序页面时，是否自动暂停本页面的视频
          autoPauseIfOpenNative: true,
          // 微信：当跳转到其它微信原生页面时，是否自动暂停本页面的视频 
          vslideGesture: true,
          // 在非全屏模式下，是否开启亮度与音量调节手势（同 page-gesture） 
          vslideGestureInFullscreen: true,
          // 在全屏模式下，是否开启亮度与音量调节手势
          codec: "hardware",
          // 解码器选择：hardware：硬解码（硬解码可以增加解码算力，提高视频清晰度。）；software：ffmpeg软解码；
          httpCache: true,
          // 是否对 http、https 视频源开启本地缓存 （不适用于m3u8等流媒体协议）
          /**
           * 播放策略：
           *  0-普通模式，适合绝大部分视频播放场景；
           *  1-平滑播放模式，增加缓冲区大小，采用open sl解码音频，避免音视频脱轨的问题，可能会降低首屏展现速度、视频帧率，出现开屏音频延迟等 适用于高码率视频的极端场景；
           *  2：M3U8优化模式，增加缓冲区大小，提升视频加载速度和流畅度，可能会降低首屏展现速度。 适用于M3U8在线播放的场景
           */
          playStrategy: 2,
          // 播放策略：0：普通模式、1：平滑播放模式、2：M3U8优化模式
          header: {},
          // HTTP 请求 Header
          isLive: false,
          // 是否为直播源，App 3.7.2+、微信小程序（2.28.1+）
          showRate: true,
          // 是否显示倍速按钮
          showFit: true,
          // 是否显示展示形式
          rateList: ["0.5", "0.8", "1.0", "1.25", "1.5", "2.0"],
          // 视频倍速 
          enableClick: false,
          // 是否启用视频点击事件
          enableDblClick: false,
          // 是否启用视频双击事件
          showWaitingTips: true,
          // 显示视频缓冲提示
          waitingCount: 5,
          // 缓冲次数等于该值时显示提示
          waitingMessage: "\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73"
          // 网络缓存提示信息
        }
      }
    },
    emits: [
      "videoClick",
      "doubleClick",
      "play",
      "pause",
      "ended",
      "timeupdate",
      "error",
      "fullscreenchange",
      "waiting",
      "progress",
      "loadedmetadata",
      "fullscreenclick",
      "controlstoggle",
      "playVideo"
    ],
    setup(__props, { expose: __expose, emit: __emit }) {
      var _a2, _b2, _c2, _d2;
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _A, _B, _C, _D, _E, _F, _G, _H, _I;
      __expose();
      const props = __props;
      const loading = (0, import_vue.ref)(false);
      const winInfo = uni.getSystemInfoSync();
      const width = (0, import_vue.ref)(((_a = props.videoOptions) == null ? void 0 : _a.width) || winInfo.windowWidth);
      const height = (0, import_vue.ref)(((_b = props.videoOptions) == null ? void 0 : _b.height) || Math.ceil(winInfo.windowHeight * 0.33));
      const winHeight = (0, import_vue.ref)(height.value);
      if (((_c = props.videoOptions) == null ? void 0 : _c.fillHeight) == true) {
        height.value = winInfo.windowHeight;
        winHeight.value = height.value;
      }
      const options = (0, import_vue.ref)({
        width: width.value,
        height: height.value,
        controls: ((_d = props.videoOptions) == null ? void 0 : _d.controls) != false,
        autoplay: ((_e = props.videoOptions) == null ? void 0 : _e.autoplay) != false,
        loop: ((_f = props.videoOptions) == null ? void 0 : _f.loop) != false,
        muted: ((_g = props.videoOptions) == null ? void 0 : _g.muted) == true,
        initialTime: (_a2 = (_h = props.videoOptions) == null ? void 0 : _h.initialTime) != null ? _a2 : 0,
        duration: (_b2 = (_i = props.videoOptions) == null ? void 0 : _i.duration) != null ? _b2 : 0,
        showPoster: ((_j = props.videoOptions) == null ? void 0 : _j.showPoster) != false,
        showProgress: ((_k = props.videoOptions) == null ? void 0 : _k.showProgress) != false,
        // showFullscreenBtn: props.videoOptions?.showFullscreenBtn,
        // showPlayBtn: props.videoOptions?.showPlayBtn,
        // showCenterPlayBtn: props.videoOptions?.showCenterPlayBtn != false,
        showCenterPlayBtn: false,
        enablePlayGesture: ((_l = props.videoOptions) == null ? void 0 : _l.enablePlayGesture) == true,
        showLoading: ((_m = props.videoOptions) == null ? void 0 : _m.showLoading) != false,
        enableProgressGesture: ((_n = props.videoOptions) == null ? void 0 : _n.enableProgressGesture) != false,
        objectFit: ((_o = props.videoOptions) == null ? void 0 : _o.objectFit) || "contain",
        // showMuteBtn: props.videoOptions?.showMuteBtn,
        playBtnPosition: ((_p = props.videoOptions) == null ? void 0 : _p.playBtnPosition) || "center",
        mobilenetHintType: (_c2 = (_q = props.videoOptions) == null ? void 0 : _q.mobilenetHintType) != null ? _c2 : 1,
        autoPauseIfNavigate: ((_r = props.videoOptions) == null ? void 0 : _r.autoPauseIfNavigate) != false,
        autoPauseIfOpenNative: ((_s = props.videoOptions) == null ? void 0 : _s.autoPauseIfOpenNative) != false,
        vslideGesture: ((_t = props.videoOptions) == null ? void 0 : _t.vslideGesture) != false,
        vslideGestureInFullscreen: ((_u = props.videoOptions) == null ? void 0 : _u.vslideGestureInFullscreen) != false,
        codec: ((_v = props.videoOptions) == null ? void 0 : _v.codec) || "hardware",
        httpCache: ((_w = props.videoOptions) == null ? void 0 : _w.httpCache) != false,
        playStrategy: (_d2 = (_x = props.videoOptions) == null ? void 0 : _x.playStrategy) != null ? _d2 : 2,
        header: ((_y = props.videoOptions) == null ? void 0 : _y.header) || {},
        isLive: ((_z = props.videoOptions) == null ? void 0 : _z.isLive) == true,
        showRate: ((_A = props.videoOptions) == null ? void 0 : _A.showRate) != false,
        rateList: ((_B = props.videoOptions) == null ? void 0 : _B.rateList) || ["0.5", "0.8", "1.0", "1.25", "1.5", "2.0"],
        enableClick: ((_C = props.videoOptions) == null ? void 0 : _C.enableClick) == true,
        enableDblClick: ((_D = props.videoOptions) == null ? void 0 : _D.enableDblClick) == true,
        showWaitingTips: ((_E = props.videoOptions) == null ? void 0 : _E.showWaitingTips) != false,
        waitingCount: ((_F = props.videoOptions) == null ? void 0 : _F.waitingCount) || 5,
        waitingMessage: ((_G = props.videoOptions) == null ? void 0 : _G.waitingMessage) || "\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73",
        fillHeight: ((_H = props.videoOptions) == null ? void 0 : _H.fillHeight) == true,
        showFit: ((_I = props.videoOptions) == null ? void 0 : _I.showFit) != false
      });
      let instance = (0, import_vue.getCurrentInstance)();
      let playerContext = null;
      const rate = (0, import_vue.ref)(1);
      const counter = (0, import_vue.ref)(0);
      const mlPlayer = (0, import_vue.ref)(null);
      const playing = (0, import_vue.ref)(false);
      const loadPlayer = (0, import_vue.computed)(() => props.showPlayer != false);
      let lastTapDiffTime = 0;
      let timer = 0;
      const emits = __emit;
      onReady(() => {
        if (!playerContext || playerContext == null) {
          setTimeout(() => {
            initVideoContext();
          }, 500);
        }
      });
      const initVideoContext = () => {
        var _a22;
        const contextEmpty = !playerContext || playerContext == null;
        const video = props.video || {};
        if (contextEmpty && video && video.url && ((_a22 = video.url) == null ? void 0 : _a22.length) > 0) {
          playerContext = uni.createVideoContext("ml-player", instance);
        }
        return playerContext;
      };
      const play = () => {
        initVideoContext();
        playing.value = true;
        loading.value = false;
        emits("play", playerContext);
      };
      const pause = () => {
        initVideoContext();
        playing.value = false;
        loading.value = false;
        emits("pause", playerContext);
      };
      const ended = () => {
        initVideoContext();
        counter.value = 0;
        loading.value = false;
        emits("ended", playerContext);
      };
      const playVideo = () => {
        initVideoContext();
        if (playerContext) {
          playerContext == null ? void 0 : playerContext.play();
          playing.value = true;
          loading.value = false;
        }
        emits("playVideo", playerContext);
      };
      const timeupdate = (event) => {
        playing.value = true;
        loading.value = false;
        emits("timeupdate", playerContext, event);
      };
      function fullscreenchange(event) {
        emits("fullscreenchange", playerContext, event);
      }
      const waiting = () => {
        loading.value = true;
        if (options.value.showWaitingTips) {
          if (counter.value >= options.value.waitingCount) {
            showToast(options.value.waitingMessage, "none");
            counter.value = 0;
            return false;
          }
          counter.value = counter.value + 1;
        }
        initVideoContext();
        emits("waiting", playerContext);
      };
      const openFit = () => {
        const fit = ["\u5305\u542B", "\u586B\u5145", "\u8986\u76D6"];
        uni.showActionSheet({
          title: "\u8BBE\u7F6E\u5C55\u793A\u5F62\u5F0F",
          alertText: "\u9009\u62E9\u5C55\u793A\u5F62\u5F0F",
          itemList: fit,
          success: function(res) {
            const index = res.tapIndex;
            const val = ["contain", "fill", "cover"][index];
            options.value.objectFit = val;
            showToast("\u5C55\u793A\u5F62\u5F0F\uFF1A" + fit[index], "none");
          }
        });
      };
      const openRate = () => {
        const rateList = options.value.rateList || ["0.5", "0.75", "1.0", "1.25", "1.5", "1.75", "2.0", "2.5"];
        if (rateList && rateList.length > 0) {
          uni.showActionSheet({
            title: "\u8BBE\u7F6E\u500D\u901F",
            alertText: "\u9009\u62E9\u500D\u901F",
            itemList: rateList,
            success: function(res) {
              const rateNum = Number(rateList[res.tapIndex]);
              if (!playerContext)
                initVideoContext();
              if (playerContext && !isNaN(rateNum)) {
                rate.value = rateNum;
                playerContext == null ? void 0 : playerContext.playbackRate(rateNum);
                showToast("\u500D\u901F\uFF1A" + rateNum, "none");
              }
            }
          });
        }
      };
      const videoError = (event) => {
        showToast("\u8D44\u6E90\u64AD\u653E\u9519\u8BEF", "error");
        playing.value = false;
        loading.value = false;
        const errInfo = { video: props.video, error: event };
        formatAppLog("error", "at components/ml-player/ml-player.vue:387", "==========\u8D44\u6E90\u64AD\u653E\u9519\u8BEF=========");
        formatAppLog("error", "at components/ml-player/ml-player.vue:388", errInfo);
        emits("error", playerContext, event);
      };
      function progress(event) {
        emits("progress", playerContext, event);
      }
      function loadedmetadata(event) {
        emits("loadedmetadata", playerContext, event);
      }
      function fullscreenclick(event) {
        emits("fullscreenclick", playerContext, event);
      }
      function controlstoggle(event) {
        emits("controlstoggle", playerContext, event);
      }
      const videoClick = () => {
        if (!(options.value.enableClick == true))
          return;
        initVideoContext();
        emits("videoClick", playerContext, props.video);
      };
      const doubleClick = () => {
        let currentTime = Date.now();
        let lastTime = lastTapDiffTime;
        lastTapDiffTime = currentTime;
        if (currentTime - lastTime < 200) {
          clearTimeout(timer);
          if (options.value.enableDblClick == true) {
            emits("doubleClick", playerContext, props.video);
          }
        } else {
          timer = setTimeout(() => {
            videoClick();
          }, 200);
        }
      };
      const showToast = (title, icon) => {
        uni.hideToast();
        uni.showToast({
          title,
          icon: icon || "none",
          mask: false,
          duration: 2e3
        });
      };
      function resetVariables() {
        if (playerContext) {
          playerContext == null ? void 0 : playerContext.pause();
          playerContext == null ? void 0 : playerContext.stop();
        }
        clearTimeout(timer);
        playerContext = null;
        mlPlayer.value = null;
        options.value = null;
        instance = null;
      }
      onHide(() => {
        if (playerContext) {
          playerContext == null ? void 0 : playerContext.pause();
        }
      });
      onUnload(() => {
        resetVariables();
      });
      (0, import_vue.onUnmounted)(() => {
        resetVariables();
      });
      const __returned__ = { props, loading, winInfo, width, height, winHeight, options, get instance() {
        return instance;
      }, set instance(v) {
        instance = v;
      }, get playerContext() {
        return playerContext;
      }, set playerContext(v) {
        playerContext = v;
      }, rate, counter, mlPlayer, playing, loadPlayer, get lastTapDiffTime() {
        return lastTapDiffTime;
      }, set lastTapDiffTime(v) {
        lastTapDiffTime = v;
      }, get timer() {
        return timer;
      }, set timer(v) {
        timer = v;
      }, emits, initVideoContext, play, pause, ended, playVideo, timeupdate, fullscreenchange, waiting, openFit, openRate, videoError, progress, loadedmetadata, fullscreenclick, controlstoggle, videoClick, doubleClick, showToast, resetVariables, ref: import_vue.ref, getCurrentInstance: import_vue.getCurrentInstance, onUnmounted: import_vue.onUnmounted, computed: import_vue.computed, get onReady() {
        return onReady;
      }, get onHide() {
        return onHide;
      }, get onUnload() {
        return onUnload;
      } };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
    return (0, import_vue.openBlock)(), (0, import_vue.createElementBlock)(
      import_vue.Fragment,
      null,
      [
        $setup.loadPlayer && $props.video && $props.video.url ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("u-video", {
          key: 0,
          class: "ml-player",
          id: "ml-player",
          ref: "mlPlayer",
          src: $props.video.url,
          poster: $props.video.poster,
          title: $props.video.title,
          controls: $setup.options.controls,
          autoplay: $setup.options.autoplay,
          loop: $setup.options.loop,
          muted: $setup.options.muted,
          initialTime: $setup.options.initialTime,
          duration: $setup.options.duration,
          showFullscreenBtn: $setup.options.controls,
          showPlayBtn: $setup.options.controls,
          showCenterPlayBtn: $setup.options.showCenterPlayBtn,
          showLoading: $setup.options.showLoading,
          enableProgressGesture: $setup.options.enableProgressGesture,
          objectFit: $setup.options.objectFit,
          showMuteBtn: $setup.options.controls,
          playBtnPosition: $setup.options.playBtnPosition,
          autoPauseIfNavigate: $setup.options.autoPauseIfNavigate,
          autoPauseIfOpenNative: $setup.options.autoPauseIfOpenNative,
          vslideGesture: $setup.options.vslideGesture,
          vslideGestureInFullscreen: $setup.options.vslideGestureInFullscreen,
          codec: $setup.options.codec,
          httpCache: $setup.options.httpCache,
          playStrategy: $setup.options.playStrategy,
          showProgress: $setup.options.showProgress,
          pageGesture: $setup.options.vslideGesture,
          mobilenetHintType: $setup.options.mobilenetHintType,
          enablePlayGesture: $setup.options.enablePlayGesture,
          isLive: $setup.options.isLive,
          onClick: (0, import_vue.withModifiers)($setup.doubleClick, ["stop"]),
          onPlay: $setup.play,
          onPause: $setup.pause,
          onEnded: $setup.ended,
          onTimeupdate: $setup.timeupdate,
          onFullscreenchange: $setup.fullscreenchange,
          onWaiting: $setup.waiting,
          onError: $setup.videoError,
          onProgress: $setup.progress,
          onLoadedmetadata: $setup.loadedmetadata,
          onFullscreenclick: $setup.fullscreenclick,
          onControlstoggle: $setup.controlstoggle,
          webkitPlaysinline: "true",
          playsinline: "true",
          xWebkitAirplay: "allow",
          x5VideoPlayerType: "h5-page",
          x5VideoOrientation: "portrait",
          style: (0, import_vue.normalizeStyle)({ width: $setup.width + "px", height: $setup.height + "px" })
        }, [
          (0, import_vue.createElementVNode)("u-scalable", { style: { position: "absolute", left: "0", right: "0", top: "0", bottom: "0" } }, [
            $setup.options.showRate || $setup.options.showFit ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)(
              "cover-view",
              {
                key: 0,
                class: "rateIcon",
                style: (0, import_vue.normalizeStyle)({ top: $setup.height / 2 + "px" })
              },
              [
                $setup.options.showRate ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("view", {
                  key: 0,
                  onClick: (0, import_vue.withModifiers)($setup.openRate, ["stop"])
                }, [
                  (0, import_vue.createElementVNode)(
                    "u-text",
                    { class: "rateText" },
                    (0, import_vue.toDisplayString)($setup.rate) + "\xD7",
                    1
                    /* TEXT */
                  )
                ])) : (0, import_vue.createCommentVNode)("v-if", true),
                $setup.options.showFit ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("view", {
                  key: 1,
                  style: { "margin-top": "2px" },
                  onClick: (0, import_vue.withModifiers)($setup.openFit, ["stop"])
                }, [
                  (0, import_vue.createElementVNode)("u-text", {
                    class: "rateText",
                    style: { "font-size": "17px", "line-height": "17px" }
                  }, "\u2699")
                ])) : (0, import_vue.createCommentVNode)("v-if", true)
              ],
              4
              /* STYLE */
            )) : (0, import_vue.createCommentVNode)("v-if", true),
            (0, import_vue.createElementVNode)("cover-view", null, [
              (0, import_vue.renderSlot)(_ctx.$slots, "default")
            ]),
            !$setup.playing || $setup.loading ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)(
              "view",
              {
                key: 1,
                class: "center-play-mask",
                style: (0, import_vue.normalizeStyle)({ width: $setup.width + "px", height: $setup.winHeight + "px" })
              },
              [
                !$setup.loading && !$setup.playing ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("cover-image", {
                  key: 0,
                  src: "data:image/png;base64,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",
                  class: "center-play-btn",
                  onClick: (0, import_vue.withModifiers)($setup.playVideo, ["stop"])
                })) : $setup.loading ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("cover-image", {
                  key: 1,
                  src: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABX9JREFUaEPtWVuME2UU/s5MFwFFCRh4gMJ2irLMbNspm0B0o/IgoIaoGORBMQZFY6IRlPhiRMVLiAleow9GhA1GgkYMvmi8BWIIKAE63e502Ww7Xa4KSiSCLnbbOTKlXdo67XR70Uqcx3/O+c73/ef/z38j1Pj5+8NTRdG1mBmnNK+ypRhuXiIx+rT5+zoQ3QtgCMAmTVKeqTHssDvVAjQ71tthCua3AK7K4miDSfHGvra2Mzlc1dA/ALAsPw4Db4UlZWUtsXO+NQlQDX3gfI9OLyTCGzSp/SGrzRfv9YlkdtsR5RbX1LB75rFaRVQtoKM/4k2LQsyGgKFJitdqV+P6PSB8aEeSmOaHvPI31r+ZBw+OG9uSWsSC4AbSezWPb2elwqoXcHzf2PS50b8ANKYwGH2lSfJCq609FnG7BOGwDZmzoKRb8wRPB/p1hQT+EkRTcnYMbAtLypJKRDgKyPbiUgZiLoFe3d8q/5gDDhrR+xi8OS/QbybEOd1SW9/wHEhEV4H59QIyhOWaR+my2gKG3ktAWzFZBq0JS/JLTiLKCggY+lICPsqbMJGQpPjzQYMD+q1gWmFVIVMQ1nW3tiWKg6oJ/Q4wloGQBFOXJslfWzZ+Q79BAL4rQXJ4KJYTUVaAakTDABcQBmhBjoBT7zj9D8ajNzNxRozNd1yTlOFhVQrLQYB+AEAw3zl/8jkRdPo/t7//yj/FpFWJrvibLaNL8yrLnTCcBNwN4ONyQ8gpgNP/wEB0MZn8aZHdobSYDkam+3918necxAFDX0bAErtJ7ARe6f/sgrgWwGxi2vzHkPCytRh29PVdnXalFkKgKTDRo3nlz4sxHQVUSqLedrMT+i0mY2veKg8CIkMw5/dIvhN5o6LeoWvHU47oE1qG0A9ggg3aJ5qkWEM78w1n4EJJo7fB5kSQ8LwmyRtqp1IdQtDQVzOwvpR3yjSn9czwHSkQULyvyTeqjkb1XqoRfRvgR0uXTropJMmZ9SOTATXW0wmBdhU4ED2heeQ3qqdRvWe2cFi7WNsvOYhxUUU5ezEDzIKaiFobM0/WwySX6A5NaztePY3qPeft2OE63TpJA0MpRmHGC2Gv8lzpOQCeDPAaTWp/r3oKtXv6DnVLYlq09lmdF9HoHU2SH8tHb9oymiPpT+hzBZM6qEXYbjciml5ATohqROeDuZNAJ1KgXRHvrEhBFao96Y1DUI1oF8D3F0V4SpOU9U2fgUBcX0uEZ227h3BnRoA/Fp4ExqjuawJHG9ePI0duNyKTXRB+KunJfIwKdoN5J6WRh6u/R4mdauFypRq6dYJqzbaeTA7CHVWUZP3pjBwxU4EY35fxNElN6DEwMrcIAH4eT2On7fR4zo08XGM81LiugyDboTPzVgrEI3cRCdssA2Y8EPYqmxpDpTrUzK2FiB8AXF6EcDgtptWmr0IW6ayIjQDmZEQwtqfIfMQ6F/wnBJTL3f8CqhvZ9fO6tDJgXSMS6GTu0rV+/dQ4pOEMBAz9TQIet0IRaEVIkt9vXNj6IecJ6NlHoI6MAMLGkEd5sH5hGoeUL+BhAr2bCWWandoM3+7Gha0fcsEktg73gsBnDkg+21eV+oWtHGnO0d6JQylcG2qdtcfO69KqQpX3S/NYXvoZsE5FLRAXjW4Zt2WP2z3YPH1/gUnZDMi6PuqyMYgx4D7/ZldwqdosQhyHkGro1iPDeDC+0LzKbY0iblWbvVNnnRopvqOAgBFZQCxcLwrprfs9/oMjDVCpvRrveQUC9eZeLyv1cxRQKdC/ZVezgOAhXTbTeJLN1NPdMwIn/2khNQtQDf0zALcz6MWwJNteQAWO9E2hodRrZjq1ut53T7ULyDxi0yoB6ZWltiDBgd7r2DR3N2KP9RdxLfhyI6cVtQAAAABJRU5ErkJggg==",
                  class: "center-loading"
                })) : (0, import_vue.createCommentVNode)("v-if", true),
                !$setup.playing || $setup.loading ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("u-text", {
                  key: 2,
                  style: { "color": "#fff", "text-align": "center" }
                }, "\u70B9\u51FB\u64AD\u653E")) : (0, import_vue.createCommentVNode)("v-if", true)
              ],
              4
              /* STYLE */
            )) : (0, import_vue.createCommentVNode)("v-if", true)
          ])
        ], 44, ["src", "poster", "title", "controls", "autoplay", "loop", "muted", "initialTime", "duration", "showFullscreenBtn", "showPlayBtn", "showCenterPlayBtn", "showLoading", "enableProgressGesture", "objectFit", "showMuteBtn", "playBtnPosition", "autoPauseIfNavigate", "autoPauseIfOpenNative", "vslideGesture", "vslideGestureInFullscreen", "codec", "httpCache", "playStrategy", "showProgress", "pageGesture", "mobilenetHintType", "enablePlayGesture", "isLive"])) : (0, import_vue.createCommentVNode)("v-if", true),
        !$setup.loadPlayer && $setup.options.showPoster && $props.video.poster ? ((0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("u-image", {
          key: 1,
          src: $props.video.poster,
          style: (0, import_vue.normalizeStyle)({ width: $setup.width + "px", height: $setup.height + "px" })
        }, null, 12, ["src"])) : (0, import_vue.createCommentVNode)("v-if", true)
      ],
      64
      /* STABLE_FRAGMENT */
    );
  }
  var __easycom_0$1 = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render$1], ["styles", [_style_0$1]], ["__file", "E:/\u5F00\u53D1\u6240\u9700/\u777F\u777F\u6E90\u7801/App/components/ml-player/ml-player.vue"]]);
  var _style_0 = { "ml-swiper-item-view-box": { "": { "position": "relative", "backgroundColor": "rgba(0,0,0,0.7)" } }, "ml-swiper-item-view-right-box": { "": { "position": "absolute", "bottom": 100, "paddingTop": 5, "paddingRight": 5, "paddingBottom": 5, "paddingLeft": 5, "right": 1, "flexWrap": "wrap", "flexDirection": "column" } }, "ml-swiper-item-view-bottom-box": { "": { "position": "absolute", "bottom": 1, "left": 0, "flexWrap": "wrap", "flexDirection": "column" } }, "center-play-mask": { "": { "position": "fixed", "top": 0, "left": 0, "alignItems": "center", "justifyContent": "center" } }, "center-play-btn": { ".center-play-mask ": { "width": "140rpx", "height": "140rpx" } }, "center-loading": { ".center-play-mask ": { "width": "100rpx", "height": "100rpx" } }, "ml-swiper-view": { "": { "backgroundColor": "rgba(0,0,0,0.7)" } }, "ml-swiper": { "": { "backgroundColor": "rgba(0,0,0,0.7)" } }, "ml-swiper-item": { "": { "backgroundColor": "rgba(0,0,0,0.7)" } }, "ml-swiper-item-video": { "": { "backgroundColor": "rgba(0,0,0,0.7)" } } };
  var _sfc_main = /* @__PURE__ */ Object.assign({ name: "ml-swiper" }, {
    __name: "ml-swiper",
    props: {
      width: {
        // 播放器宽度
        type: Number,
        default: 0,
        required: false
      },
      height: {
        // 播放器高度
        type: Number,
        default: 0,
        required: false
      },
      rightStyle: {
        // 自定义右侧样式
        type: [Object, String],
        default: {},
        required: false
      },
      bottomStyle: {
        // 自定义底部样式
        type: [Object, String],
        default: {},
        required: false
      },
      videoList: {
        // 资源列表
        type: Array,
        default: [],
        required: true
      },
      count: {
        // 临界点，当资源剩余多少时触发加载更多，默认2条
        type: Number,
        default: 2,
        required: false
      },
      showPlay: {
        // 页面显示时 是否播放
        type: Boolean,
        default: true,
        required: false
      },
      hidePause: {
        // 程序进入到后台时 是否暂停播放
        type: Boolean,
        default: true,
        required: false
      }
    },
    emits: [
      "change",
      // 视频滑动事件
      "play",
      // 视频开始播放
      "pause",
      // 视频暂停播放
      "ended",
      // 视频播放结束
      "error",
      // 视频播放出错
      "waiting",
      // 出视频出现缓冲
      "videoClick",
      // 视频点击事件
      "doubleClick",
      // 视频双击事件
      "loadMore",
      // 加载更多(index, size); // index:当前索引，size:列表长度
      "maskClick"
      // 蒙层被点击事件
    ],
    setup(__props, { expose: __expose, emit: __emit }) {
      __expose();
      const win = uni.getSystemInfoSync();
      const props = __props;
      const videoOptions = (0, import_vue.ref)({
        width: props.width || win.windowWidth,
        // 宽度
        height: props.height || win.windowHeight,
        // 高度
        controls: false,
        // 不显示控制条
        initialTime: 0,
        // 初始播放位置，单位为秒（s）
        vslideGesture: false,
        // 不开启亮度与音量调节手势
        showRate: false,
        // 不使用倍速功能
        showFit: false,
        // 不使用视频展示形式功能
        enableClick: true,
        // 开启单击事件
        enableDblClick: true,
        // 开启双击双击
        autoplay: true,
        // 是否自动播放
        showPoster: true,
        // 是否展示预览图
        loop: true,
        // 是否循环播放
        muted: false,
        // 是否静音播放
        objectFit: "contain"
        // 视频展示样式
      });
      let context = null;
      const current = (0, import_vue.ref)(0);
      const realList = (0, import_vue.computed)(() => props.videoList || []);
      const locked = (0, import_vue.ref)(false);
      const playing = (0, import_vue.ref)(false);
      const loading = (0, import_vue.ref)(true);
      const fullScreen2 = (0, import_vue.computed)(() => {
        return {
          width: videoOptions.value.width + "px",
          height: videoOptions.value.height + "px"
        };
      });
      const emits = __emit;
      const onchange = (event) => {
        if (locked.value)
          return;
        locked.value = true;
        setTimeout(() => {
          locked.value = false;
        }, 500);
        const index = event.detail.current;
        customPause();
        current.value = index;
        initSwiperData();
      };
      const initSwiperData = () => {
        isLoadMore();
        let rawLen = realList.value.length;
        emits("change", current.value, rawLen);
      };
      const isLoadMore = () => {
        let rawLen = realList.value.length;
        let count = Number(isNaN(props.count) ? 2 : props.count);
        let num = Number(Number(rawLen) - count);
        if (num == current.value) {
          emits("loadMore", current.value, rawLen);
        }
      };
      let lastTouchTime = 0;
      const touchEnd = (_e) => {
        if (Date.now() - lastTouchTime < 200) {
          doubleClick(context, realList.value[current.value]);
          lastTouchTime = 0;
          return;
        }
        lastTouchTime = Date.now();
      };
      const play = (vcontext) => {
        if (vcontext)
          context = vcontext;
        playing.value = true;
        loading.value = false;
        emits("play", context);
      };
      const pause = (vcontext) => {
        if (vcontext)
          context = vcontext;
        playing.value = false;
        emits("pause", context);
      };
      const waiting = (vcontext) => {
        if (vcontext)
          context = vcontext;
        loading.value = true;
        emits("waiting", context);
      };
      const ended = (vcontext) => {
        if (vcontext)
          context = vcontext;
        emits("ended", context);
      };
      const error = (vcontext, event) => {
        if (vcontext)
          context = vcontext;
        loading.value = false;
        emits("error", context, event);
      };
      const videoClick = (vcontext, video) => {
        if (vcontext)
          context = vcontext;
        if (playing.value === true) {
          customPause();
        } else {
          customPlay();
        }
        emits("videoClick", context, video);
      };
      const doubleClick = (vcontext, video) => {
        if (vcontext)
          context = vcontext;
        emits("doubleClick", context, video);
      };
      const playVideo = (video, index) => {
        customPlay();
        emits("maskClick", video, index);
      };
      function customPause() {
        if (context) {
          try {
            context == null ? void 0 : context.pause();
            playing.value = false;
          } catch (e) {
            formatAppLog("error", "at components/ml-swiper/ml-swiper.vue:366", e);
          }
        }
      }
      function customPlay() {
        if (context) {
          formatAppLog("log", "at components/ml-swiper/ml-swiper.vue:373", context);
          try {
            context == null ? void 0 : context.play();
            playing.value = true;
            loading.value = false;
          } catch (e) {
            formatAppLog("error", "at components/ml-swiper/ml-swiper.vue:379", e);
          }
        }
      }
      function customStop() {
        if (context) {
          try {
            context == null ? void 0 : context.stop();
          } catch (e) {
            formatAppLog("error", "at components/ml-swiper/ml-swiper.vue:389", e);
          }
        }
      }
      onShow(() => {
        if (props.showPlay) {
          customPlay();
        }
      });
      (0, import_vue.onMounted)(() => {
      });
      onHide(() => {
        if (props.hidePause) {
          customPause();
        }
      });
      onUnload(() => {
        lastTouchTime = 0;
        customStop();
        current.value = 0;
        locked.value = false;
        playing.value = false;
        loading.value = true;
        context = null;
      });
      const __returned__ = { win, props, videoOptions, get context() {
        return context;
      }, set context(v) {
        context = v;
      }, current, realList, locked, playing, loading, fullScreen: fullScreen2, emits, onchange, initSwiperData, isLoadMore, get lastTouchTime() {
        return lastTouchTime;
      }, set lastTouchTime(v) {
        lastTouchTime = v;
      }, touchEnd, play, pause, waiting, ended, error, videoClick, doubleClick, playVideo, customPause, customPlay, customStop, computed: import_vue.computed, onMounted: import_vue.onMounted, ref: import_vue.ref, get onHide() {
        return onHide;
      }, get onShow() {
        return onShow;
      }, get onUnload() {
        return onUnload;
      } };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  });
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    const _component_ml_player = resolveEasycom((0, import_vue.resolveDynamicComponent)("ml-player"), __easycom_0$1);
    const _component_swiper_item = (0, import_vue.resolveComponent)("swiper-item");
    const _component_swiper = (0, import_vue.resolveComponent)("swiper");
    return (0, import_vue.openBlock)(), (0, import_vue.createElementBlock)("view", {
      class: "ml-swiper-view",
      renderWhole: true
    }, [
      $setup.realList && $setup.realList.length > 0 ? ((0, import_vue.openBlock)(), (0, import_vue.createBlock)(_component_swiper, {
        key: 0,
        class: "ml-swiper",
        current: $setup.current,
        circular: false,
        vertical: true,
        "skip-hidden-item-layout": true,
        style: (0, import_vue.normalizeStyle)($setup.fullScreen),
        onTouchend: $setup.touchEnd,
        onChange: $setup.onchange
      }, {
        default: (0, import_vue.withCtx)(() => [
          ((0, import_vue.openBlock)(true), (0, import_vue.createElementBlock)(
            import_vue.Fragment,
            null,
            (0, import_vue.renderList)($setup.realList, (video, index) => {
              return (0, import_vue.openBlock)(), (0, import_vue.createBlock)(
                _component_swiper_item,
                {
                  class: "ml-swiper-item",
                  key: index
                },
                {
                  default: (0, import_vue.withCtx)(() => [
                    (0, import_vue.createElementVNode)(
                      "cover-view",
                      {
                        class: "ml-swiper-item-view-box",
                        style: (0, import_vue.normalizeStyle)($setup.fullScreen)
                      },
                      [
                        (0, import_vue.createVNode)(_component_ml_player, {
                          showPlayer: $setup.current === index,
                          video,
                          videoOptions: $setup.videoOptions,
                          onVideoClick: $setup.videoClick,
                          onDoubleClick: $setup.doubleClick,
                          onPlay: $setup.play,
                          onPause: $setup.pause,
                          onWaiting: $setup.waiting,
                          onEnded: $setup.ended,
                          onError: $setup.error
                        }, null, 8, ["showPlayer", "video", "videoOptions"]),
                        (0, import_vue.createElementVNode)(
                          "cover-view",
                          {
                            class: "ml-swiper-item-view-right-box",
                            style: (0, import_vue.normalizeStyle)($props.rightStyle)
                          },
                          [
                            (0, import_vue.renderSlot)(_ctx.$slots, "right", {
                              video,
                              index
                            })
                          ],
                          4
                          /* STYLE */
                        ),
                        (0, import_vue.createElementVNode)(
                          "cover-view",
                          {
                            class: "ml-swiper-item-view-bottom-box",
                            style: (0, import_vue.normalizeStyle)($props.bottomStyle)
                          },
                          [
                            (0, import_vue.renderSlot)(_ctx.$slots, "bottom", {
                              video,
                              index
                            })
                          ],
                          4
                          /* STYLE */
                        )
                      ],
                      4
                      /* STYLE */
                    )
                  ]),
                  _: 2
                  /* DYNAMIC */
                },
                1024
                /* DYNAMIC_SLOTS */
              );
            }),
            128
            /* KEYED_FRAGMENT */
          ))
        ]),
        _: 3
        /* FORWARDED */
      }, 8, ["current", "style"])) : (0, import_vue.createCommentVNode)("v-if", true)
    ]);
  }
  var __easycom_0 = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["styles", [_style_0]], ["__file", "E:/\u5F00\u53D1\u6240\u9700/\u777F\u777F\u6E90\u7801/App/components/ml-swiper/ml-swiper.vue"]]);
  var request = (_url, data, args) => {
    return new Promise((resolve, reject) => {
      let isShowLoading = false;
      const {
        method = "POST",
        header,
        isLoad = true
      } = args;
      if (isLoad) {
        uni.showLoading({
          title: "\u52A0\u8F7D\u4E2D...",
          mask: true
        });
        isShowLoading = true;
      }
      const url = _url;
      uni.request({
        url,
        method,
        data,
        header,
        success: (res) => {
          const { data: data2 } = res;
          isShowLoading && uni.hideLoading();
          switch (data2.code) {
            case 0:
              resolve(data2);
              break;
            default:
              if (data2.msg && !data2.msg.includes("\u67E5\u8BE2\u6570\u636E\u4E0D\u5B58\u5728")) {
                uni.showToast({
                  title: data2.msg || "\u7F51\u7EDC\u51FA\u9519",
                  icon: "none"
                });
              }
              if (data2.data.reload) {
                uni.clearStorageSync();
                uni.reLaunch({
                  url: "/pagesSub/pages/login/login"
                });
              }
              reject(data2.msg || "\u7F51\u7EDC\u51FA\u9519");
              break;
          }
        },
        complete: (err) => {
          isShowLoading && uni.hideLoading();
          formatAppLog("log", "at apis/https.js:53", url, data, err);
        }
      });
    });
  };
  var requestNew = (_url, data, args) => {
    return new Promise((resolve, reject) => {
      const {
        method = "POST",
        header,
        isLoad = true
      } = args;
      let isShowLoading = false;
      if (isLoad) {
        uni.showLoading({
          title: "\u52A0\u8F7D\u4E2D...",
          mask: true
        });
        isShowLoading = true;
      }
      const url = _url;
      uni.request({
        url,
        method,
        data,
        header,
        success: (res) => {
          const { data: data2 } = res;
          isShowLoading && uni.hideLoading();
          switch (data2.ErrCode) {
            case "0":
              resolve(data2);
              break;
            default:
              if (data2.ErrCode !== "60001") {
                uni.showToast({
                  title: data2.ErrMsg || "\u7F51\u7EDC\u51FA\u9519",
                  icon: "none"
                });
              }
              resolve(data2);
              break;
          }
        },
        complete: (err) => {
          isShowLoading && uni.hideLoading();
          formatAppLog("log", "at apis/httpsNew.js:53", url, data, err);
        }
      });
    });
  };
  var userInfoStore = (0, import_pinia.defineStore)(
    "userInfo",
    () => {
      const token = (0, import_vue.ref)("");
      const user = (0, import_vue.ref)({});
      const teamID = (0, import_vue.ref)(0);
      const teamName = (0, import_vue.ref)("");
      const companyID = (0, import_vue.ref)(0);
      const companyName = (0, import_vue.ref)("");
      const roleName = (0, import_vue.ref)("");
      const roleID = (0, import_vue.ref)(0);
      const beanRate = (0, import_vue.ref)(0);
      function setInfo(data) {
        const item = data.user.roles[0];
        user.value = data.user;
        token.value = data.token;
        if (item != void 0) {
          teamID.value = item.teamID;
          teamName.value = item.teamName;
          companyID.value = item.companyID;
          companyName.value = item.companyName;
          roleName.value = item.roleName;
          roleID.value = item.role_ID;
          beanRate.value = item.beanRate;
        }
      }
      function setItem(name, data) {
        switch (name) {
          case "token":
            token.value = data;
            break;
          case "user":
            user.value = data;
            break;
          case "teamID":
            teamID.value = data;
            break;
          case "teamName":
            teamName.value = data;
            break;
          case "companyID":
            companyID.value = data;
            break;
          case "companyName":
            companyName.value = data;
            break;
          case "roleName":
            roleName.value = data;
            break;
          case "roleID":
            roleID.value = data;
            break;
          case "beanRate":
            beanRate.value = data;
            break;
        }
      }
      function clear() {
        token.value = "";
        user.value = {};
        teamID.value = 0;
        teamName.value = "";
        companyID.value = 0;
        companyName.value = "";
        roleName.value = "";
        roleID.value = 0;
        beanRate.value = 0;
      }
      return {
        token,
        user,
        teamID,
        teamName,
        companyID,
        companyName,
        roleName,
        roleID,
        beanRate,
        setInfo,
        setItem,
        clear
      };
    },
    {
      unistorage: true
    }
  );
  var userInfoStore$1 = userInfoStore;
  var httpsNewWithoutNotion = (_url, data, args) => {
    return new Promise((resolve, reject) => {
      const {
        method = "POST",
        header,
        isLoad = true
      } = args;
      let isShowLoading = false;
      if (isLoad) {
        uni.showLoading({
          title: "\u52A0\u8F7D\u4E2D...",
          mask: true
        });
        isShowLoading = true;
      }
      const url = _url;
      uni.request({
        url,
        method,
        data,
        header,
        success: (res) => {
          const { data: data2 } = res;
          isShowLoading && uni.hideLoading();
          switch (data2.ErrCode) {
            case "0":
              resolve(data2);
              break;
            default:
              resolve(data2);
              break;
          }
        },
        complete: (err) => {
          isShowLoading && uni.hideLoading();
          formatAppLog("log", "at apis/httpsNewWithoutNotion.js:50", url, data, err);
        }
      });
    });
  };
  var apis = {
    AppUser: {
      Login: (data) => _http("appUser/login", data),
      Register: (data) => _http("appUser/register", data),
      CheckExist: (data) => _http("appUser/checkExist", data, "GET"),
      UserList: (data) => _http("appUser/getUserList", data, "GET"),
      CheckUserStatus: (data) => _http1("Clothing/User/CheckUserStatus", data, "POST"),
      AddLoginLog: (data) => _http1("Clothing/Staff/AddLoginLog", data, "POST"),
      ModifyPwd: (data) => _http1("Clothing/Staff/ModifyPwd", data, "POST"),
      AddMember: (data) => _http1("Clothing/User/AddMember", data, "POST"),
      CheckUserAuth: (data) => _http1("Clothing/User/CheckUserAuth", data, "POST"),
      CheckCheckMemberStatus: (data) => _http1("Clothing/User/CheckCheckMemberStatus", data, "POST")
    },
    AppRole: {
      List: () => _http("appRole/getAppRoleList", null, "GET")
    },
    Company: {
      List: () => _http("company/getCompanyList", null, "GET"),
      Join: (data) => _http("company/joinCompany", data),
      Check: (data) => _http("companyApply/optApply", data, "PUT"),
      Apply: (data) => _http("companyApply/findCompanyApply", data, "GET"),
      DelUser: (data) => _http("company/deleteStaff", data, "DELETE")
    },
    Team: {
      List: (data) => _http("team/getTeamList", data, "GET"),
      Join: (data) => _http("team/joinTeam", data),
      ApplyDetail: (data) => _http("teamApply/findTeamApply", data, "GET"),
      Check: (data) => _http("teamApply/optApply", data, "PUT"),
      DelUser: (data) => _http("team/deleteMember", data, "DELETE")
    },
    Staff: {
      GroupList: (data) => _http1("Clothing/Staff/GroupList", data, "POST"),
      UserList: (data) => _http1("Clothing/Staff/UserList", data, "POST"),
      UserListEnhanced: (data) => _http1("Clothing/Staff/getUserListEnhanced", data, "POST"),
      GetCompanyList: () => _http1("Clothing/Staff/GetCompanyList", null, "POST"),
      RoleList: () => _http1("Clothing/Staff/RoleList", null, "POST"),
      AddGroup: (data) => _http1("Clothing/Staff/AddGroup", data, "POST"),
      AddTeam: (data) => _http1("Clothing/Staff/AddTeam", data, "POST"),
      EditTeam: (data) => _http1("Clothing/Staff/EditTeam", data, "POST"),
      DeleteTeam: (data) => _http1("Clothing/Staff/DeleteTeam", data, "POST"),
      UpdateGroup: (data) => _http1("Clothing/Staff/UpdateGroup", data, "POST"),
      DeleteGroup: (data) => _http1("Clothing/Staff/DeleteGroup", data, "POST"),
      ExistCompany: (data) => _http1("Clothing/Staff/ExistCompany", data, "POST"),
      AddCompany: (data) => _http1("Clothing/Staff/AddCompany", data, "POST"),
      UserRoleList: (data) => _http1("Clothing/Staff/UserRoleList", data, "POST"),
      UserListByComp: (data) => _http2("Clothing/Staff/UserListByComp", data, "POST")
    },
    CroppingRecord: {
      List: (data) => _http("croppingRecord/getCroppingRecordList", data, "GET"),
      Detail: (data) => _http("croppingRecord/findCroppingRecord", data, "GET"),
      Update: (data) => _http("croppingRecord/updateCroppingRecord", data, "PUT"),
      Add: (data) => _http("croppingRecord/createCroppingRecord", data),
      Delete: (data) => _http("croppingRecord/deleteCroppingRecord", data, "DELETE"),
      AddNew: (data) => _http("Clothing/CroppingRecord/createCroppingRecord", data, "POST"),
      UpdatePrintStatus: (data) => _http1("Clothing/CroppingRecord/UpdatePrintStatus", data, "POST"),
      GetPrintStatus: (data) => _http1("Clothing/CroppingRecord/GetPrintStatus", data, "POST")
    },
    Style: {
      List: (data) => _http("style/getStyleList", data, "GET"),
      Add: (data) => _http("style/createStyle", data),
      Detail: (data) => _http("style/findStyle", data, "GET"),
      Update: (data) => _http("style/updateStyle", data, "PUT"),
      DeleteStyle: (data) => _http1("Clothing/Process/DeleteStyle", data, "POST"),
      StyleList: (data) => _http1("Clothing/CroppingRecord/StyleList", data, "POST")
    },
    Cloth: {
      List: (data) => _http("cloth/getClothList", data, "GET"),
      Add: (data) => _http("cloth/creatCloth", data),
      Detail: (data) => _http("cloth/findCloth", data, "GET"),
      Update: (data) => _http("cloth/updateCloth", data, "PUT"),
      AddCloth: (data) => _http1("Clothing/Cloth/AddCloth", data, "POST"),
      UpdateCloth: (data) => _http1("Clothing/Cloth/UpdateCloth", data, "POST"),
      ClothListByPage: (data) => _http1("Clothing/Cloth/ClothListByPage", data, "POST")
    },
    Wallet: {
      List: (data) => _http("userWallet/getUserWalletList", data, "GET"),
      My: () => _http("userWallet/getMyWalletList", null, "GET"),
      QueryWallet: (data) => _http1("Clothing/UserWallet/QueryWallet", data, "POST"),
      ExportWallet: (data) => _http1("Clothing/UserWallet/Export2", data, "POST"),
      WageSettle: (data) => _http1("Clothing/UserWallet/WageSettle", data, "POST"),
      WageSettleCancel: (data) => _http1("Clothing/UserWallet/WageSettleCancel", data, "POST")
    },
    Job: {
      List: (data) => _http("jobQuestion/getJobQuestionList", data, "GET"),
      Detail: (data) => _http("job/findJob", data, "GET"),
      Question: (data) => _http("jobQuestion/findJobQuestion", data, "GET"),
      Update: (data) => _http("jobQuestion/handleJobQuestion", data, "PUT"),
      Add: (data) => _http("jobQuestion/createJobQuestion", data),
      Job: (data) => _http("job/getJobList", data, "GET"),
      Process: (data) => _http("job/getJobGroupByProcess", data, "GET"),
      ToApply: (data) => _http("job/jobAuditApply", data, "PUT"),
      // 更改job表实际完成数量及金额
      Check: (data) => _http("job/jobAuditOpt", data, "PUT"),
      //审核  修改 job表记录的状态 2，4
      ChangeWorker: (data) => _http("job/changeWorker", data, "PUT"),
      Task: (data) => _http("job/postJobList", data),
      //添加 job 表记录
      Wages: (data) => _http("job/getWagesDetail", data, "GET"),
      Apply: {
        Detail: (data) => _http("jobApply/findJobApply", data, "GET"),
        Add: (data) => _http("jobApply/createJobApply", data),
        Check: (data) => _http("jobApply/optApply", data, "PUT")
      },
      JobReceiveList: (data) => _http1("Clothing/Job/JobReceiveList", data, "POST"),
      JobReceiveDetail: (data) => _http1("Clothing/Job/JobReceiveDetail", data, "POST"),
      UpdateJobStatus: (data) => _http1("Clothing/Job/UpdateJobStatus", data, "POST"),
      DeleteJob: (data) => _http2("Clothing/Job/DeleteJob", data, "POST"),
      AddJob: (data) => _http1("Clothing/Job/AddJob", data, "POST"),
      AddJobForOther: (data) => _http1("Clothing/Job/AddJobForOther", data, "POST"),
      AddJobAll: (data) => _http1("Clothing/Job/AddJobAll", data, "POST"),
      JobFinishList: (data) => _http2("Clothing/Job/JobFinishList", data, "POST"),
      JobReceiveListByCroppingRecord: (data) => _http2("Clothing/Job/JobReceiveListByCroppingRecord", data, "POST"),
      UpdateJobPrice: (data) => _http1("Clothing/Job/UpdateJobPrice", data, "POST")
    },
    Inventory: {
      Stock: (data) => _http("inventory/getInventoryList", data, "GET")
    },
    Process: {
      List: (data) => _http("process/getProcessList", data, "GET"),
      ListNew: (data) => _http1("Clothing/Process/ProcessListNew", data, "POST"),
      Detail: (data) => _http("process/findProcess", data, "GET"),
      Add: (data) => _http("process/createProcess", data),
      Update: (data) => _http("process/updateProcess", data, "PUT"),
      UpdateProcess: (data) => _http1("Clothing/Process/UpdateProcess", data, "POST"),
      AddProcess: (data) => _http1("Clothing/Process/AddProcess", data, "POST"),
      ProcessList: (data) => _http1("Clothing/Process/ProcessList", data, "POST"),
      ProcessListByPage: (data) => _http1("Clothing/Process/ProcessListByPage", data, "POST"),
      GetSizeQuanlity: (data) => _http1("Clothing/Process/GetSizeQuanlity", data, "POST"),
      GetList: (data) => _http1("Clothing/Process/StyleList", data, "POST"),
      GetStandard: (data) => _http1("Clothing/Process/GetStandard", data, "POST"),
      AddStandard: (data) => _http2("Clothing/Process/AddStandard", data, "POST"),
      GetMyStandard: (data) => _http2("Clothing/Process/GetMyStandard", data, "POST"),
      StyleSettle: (data) => _http1("Clothing/Process/StyleSettle", data, "POST"),
      StyleSettleCancel: (data) => _http1("Clothing/Process/StyleSettleCancel", data, "POST")
    },
    Chat: {
      GroupList: (data) => _http1("IM/Group/GroupList", data, "POST"),
      AddGroup: (data) => _http2("IM/Group/AddGroup", data, "POST"),
      CreateGroup: (data) => _http1("IM/Group/CreateGroup", data, "POST"),
      GetUser: (data) => _http1("IM/Chat/GetUser", data, "POST")
    },
    Message: {
      List: (data) => _http("msgBox/getMyMsgBoxList", data, "GET"),
      Send: (data) => _http("msgBox/getMySendMsgList", data, "GET"),
      Read: (data) => _http("msgBox/setRead", data, "GET")
    },
    Banner: {
      List: () => _http("banner/getBannerList", null, "GET"),
      Find: (data) => _http("banner/findBanner", data, "GET")
    },
    Computation: {
      Do: (data) => _http("computation/doComputation", data)
    },
    Order: {
      List: (data) => _http("order/getOrderList", data, "GET"),
      Detail: (data) => _http("order/findOrder", data, "GET"),
      Goods: () => _http("rechargeOption/getRechargeOptionList", null, "GET"),
      Add: (data) => _http("order/createOrder", data),
      Pay: (data) => _http("order/payOrder", data)
    },
    Common: {
      Request: (uri, data, method) => _http(uri, data, method),
      Request1: (uri, data, method) => _http1(uri, data, method),
      GetInfo: (data) => _http1("Clothing/Common/GetInfo", data, "POST"),
      AddColors: (data) => _http1("Clothing/Common/AddColors", data, "POST"),
      GetColors: (data) => _http1("Clothing/Common/GetColors", data, "POST"),
      // 日期相关API
      Today: (data) => _http1("Clothing/Date/Today", data, "GET"),
      Now: (data) => _http1("Clothing/Date/Now", data, "GET")
    },
    Question: {
      QuestionList: (data) => _http1("Clothing/Job/QuestionList", data, "POST"),
      ReplyQuestion: (data) => _http1("Clothing/Job/ReplyQuestion", data, "POST")
    },
    Adv: {
      AdvList: (data) => _http2("ADV/AdvInfo/AdvList", data, "POST"),
      AddAdv: (data) => _http2("ADV/AdvInfo/AddAdv", data, "POST"),
      DeleteAdv: (data) => _http1("ADV/AdvInfo/DeleteAdv", data, "POST"),
      PayAdv: (data) => _http1("ADV/AdvInfo/PayAdv", data, "POST"),
      BigList: (data) => _http2("ADV/AdvInfo/BigList", data, "POST"),
      SmallList: (data) => _http2("ADV/AdvInfo/SmallList", data, "POST"),
      BuyList: (data) => _http1("ADV/AdvInfo/BuyList", data, "POST"),
      AdvDetial: (data) => _http1("ADV/AdvInfo/AdvDetail", data, "POST")
    },
    Url: {
      baseUrl: "http://*********:8889",
      baseUrlBiz: "http://*********:8890",
      //baseUrlBiz:'http://localhost:8890',
      //baseUrl:'https://svapi.ruiruicaikuai.com',  //小程序
      //baseUrlBiz:'https://wxapi.ruiruicaikuai.com',  //小程序
      updateUrl: "https://wxapi.ruiruicaikuai.com/",
      baseUrlFileUpload: "https://wxapi.ruiruicaikuai.com/api/Files/Upload"
    },
    App: {
      vuex_version: "1.3.7",
      agent_code: ""
      // agent_code:"CE3B60E851FF4CAC80B56FE8EAE4505F"//代理商 温s
      // agent_code:"B1305630EB3D4BC89DD2B51B6483684D"//18632699270  河北永清
      // agent_code:"052A54961CCC41F094F68A3752750C28"//河北沧州  鲍鱼哥
      // agent_code:"23FC437A08F444DFB94253BE20D9461F"//河北永清 赵喜波
      // agent_code:"0C65033E5FAC4F15A3A384CC256456C5"//河北永清 柴国祥
      // agent_code:"A3460E0FC268458C85C9AEF42AF5F64C"//河北沧州 阿军 
      // agent_code:"15A21B2B6B5E4420989FFDEFE0C7D695"//河北永清 刘中林
      // agent_code:"68DB2E4F11854FF3A8F969E7B5206B0E"//浙江杭州 刘学
      // agent_code:"517E7BA8811449CCAC299119A79B203C"//广州 欣欣女装
      // agent_code:"780C3DDAF0094C2CBA192B5565693D1F"//河北永清 邹雅帅
      /*
      		18632699270	18632699270	河北永清	B1305630EB3D4BC89DD2B51B6483684D
      		鲍鱼哥	13301257798	河北沧州	052A54961CCC41F094F68A3752750C28
      		赵喜波	15776020343	河北永清	23FC437A08F444DFB94253BE20D9461F
      		柴国祥	19943728940	河北永清	0C65033E5FAC4F15A3A384CC256456C5
      		阿军 	17094751255	河北沧州	A3460E0FC268458C85C9AEF42AF5F64C
      		刘中林	13260072678	河北永清	15A21B2B6B5E4420989FFDEFE0C7D695
      		
      		刘学	  	18967138776	浙江杭州	68DB2E4F11854FF3A8F969E7B5206B0E
      		欣欣女装	13189018689	广州		517E7BA8811449CCAC299119A79B203C
      		邹雅帅	13522291922	河北永清	780C3DDAF0094C2CBA192B5565693D1F
      
      		*/
    }
  };
  var baseUrl = "http://*********:8889";
  var baseUrlBiz = "http://*********:8890";
  var _http = (_url, data, method = "POST") => {
    const { token } = userInfoStore$1();
    let url = baseUrl + "/api/" + _url;
    return request(url, data, {
      header: {
        "x-token": token
      },
      method
    });
  };
  var _http1 = (_url, data, method = "POST") => {
    userInfoStore$1();
    let url = baseUrlBiz + "/api/" + _url;
    return requestNew(url, data, {
      header: {
        //'x-token': token
      },
      method
    });
  };
  var _http2 = (_url, data, method = "POST") => {
    userInfoStore$1();
    let url = baseUrlBiz + "/api/" + _url;
    return httpsNewWithoutNotion(url, data, {
      header: {
        //'x-token': token
      },
      method
    });
  };

  // ../../../../睿睿源码/App/unpackage/dist/dev/.nvue/pagesSub/pages/adv/buyList.js
  var import_vue2 = __toESM(require_vue());
  var import_pinia2 = __toESM(require_pinia());
  var _style_02 = { "imgV": { "": { "width": 100, "textAlign": "center", "minHeight": 65 } }, "work": { "": { "backgroundColor": "#ffffff", "marginTop": "10rpx", "marginRight": "10rpx", "marginBottom": "10rpx", "marginLeft": "10rpx", "borderRadius": 6 } }, "title": { ".work ": { "display": "flex", "alignItems": "center", "height": "80rpx", "paddingTop": 0, "paddingRight": "20rpx", "paddingBottom": 0, "paddingLeft": "20rpx", "borderBottomWidth": 1, "borderBottomStyle": "solid", "borderBottomColor": "#e5e7eb" } }, "menu": { ".work ": { "gridTemplateColumns": "repeat(3, minmax(0, 1fr))", "gap": "30rpx", "paddingTop": "30rpx", "paddingRight": 0, "paddingBottom": "30rpx", "paddingLeft": 0, "fontSize": "24rpx", "textAlign": "center" } }, "item": { ".work .menu ": { "color": "#999999" }, ".work .menu_1 ": { "color": "#999999", "textAlign": "center" }, ".search ": { "display": "flex", "alignItems": "center", "gap": "20rpx", "fontSize": "28rpx" } }, "img": { ".work .menu .item ": { "height": "128rpx", "width": "128rpx", "borderRadius": 6, "marginTop": 0, "marginBottom": "10rpx" }, ".work .menu_1 .item ": { "height": "100rpx", "width": "100rpx", "borderRadius": 6, "marginTop": "10rpx", "marginRight": "10rpx", "marginBottom": "20rpx", "marginLeft": "10rpx" }, ".top_nav .top_content ": { "width": "44rpx", "height": "44rpx" } }, "menu_1": { ".work ": { "gridTemplateColumns": "repeat(4, minmax(0, 1fr))", "gap": "30rpx", "paddingTop": "30rpx", "paddingRight": 0, "paddingBottom": "30rpx", "paddingLeft": 0, "fontSize": "24rpx", "textAlign": "center" } }, "search": { "": { "gridTemplateColumns": "repeat(1, minmax(0, 1fr))", "paddingTop": "20rpx", "paddingRight": "20rpx", "paddingBottom": "20rpx", "paddingLeft": "20rpx", "gap": "20rpx", "backgroundColor": "#ffffff" }, ".top_nav .top_content ": { "position": "absolute", "right": 20, "top": "35rpx" } }, "label": { ".search .item ": { "flexShrink": 0 } }, "input": { ".search .item ": { "flex": 1, "backgroundColor": "#f8f8f8", "borderRadius": "10rpx", "paddingTop": 0, "paddingRight": "20rpx", "paddingBottom": 0, "paddingLeft": "20rpx", "minHeight": "60rpx" } }, "value": { ".search .item .input ": { "display": "flex", "alignItems": "center", "justifyContent": "space-between", "minHeight": "60rpx", "fontFamily::after": '"iconfont"', "content::after": '"\\e840"', "color::after": "#999999", "content:empty::before": '"\u8BF7\u9009\u62E9"', "color:empty::before": "#808080" } }, "submit": { ".search ": { "backgroundColor": "#007aff", "color": "#ffffff" } }, "btnGroup": { ".search ": { "!width": "100rpx" } }, "submit_deploy": { "": { "position": "fixed", "left": "20rpx", "right": "20rpx", "backgroundColor": "#007aff", "color": "#ffffff", "height": "80rpx", "bottom": "10rpx" } }, "person-head": { "": { "position": "relative", "backgroundColor": "#ffffff", "marginLeft": "20rpx", "marginRight": "20rpx" } }, "videoTitle": { "": { "paddingTop": 5, "paddingRight": 5, "paddingBottom": 5, "paddingLeft": 5, "color": "#de4a00", "fontSize": 13, "lines": 13, "whiteSpace": "normal" } }, "tabsviewContent": { "": { "position": "fixed", "!minWidth": "750rpx" } }, "tabsview": { "": { "!minWidth": "750rpx" } }, "top_nav": { "": { "position": "fixed", "top": 0, "left": 0, "right": 0, "backgroundImage": "linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0))" } }, "top_content": { ".top_nav ": { "paddingTop": "30rpx", "flexDirection": "row", "alignItems": "center", "justifyContent": "center" } }, "player": { ".top_nav .top_content ": { "position": "absolute", "left": 20, "top": "35rpx" } }, "content_btn": { ".top_nav .top_content ": { "flexDirection": "row", "width": 220, "alignItems": "center", "justifyContent": "space-around" } }, "content_item": { ".top_nav .top_content .content_btn ": { "position": "relative", "height": 30 } }, "line_on": { ".top_nav .top_content .content_btn .content_item ": { "position": "absolute", "width": 50, "height": 2, "backgroundColor": "#FFFFFF", "bottom": 0, "left": 2, "borderRadius": "4rpx" } }, "item_title": { ".top_nav .top_content .content_btn .content_item ": { "color": "#dcdcdc", "fontSize": "36rpx", "fontWeight": "bold" } }, "i_on": { ".top_nav .top_content .content_btn .content_item ": { "fontWeight": "bold", "fontSize": "38rpx", "!color": "#FFFFFF" } } };
  var effect3d = true;
  var effect3dMargin = 40;
  var autoplay = false;
  var vertical = false;
  var fullScreen = true;
  var topFloat = true;
  var fotterFloat = true;
  var mode = "round";
  var indicatorPos = "bottomCenter";
  var dotIndex = 0;
  var dotFloatIndex = 0;
  var _sfc_main2 = {
    __name: "buyList",
    setup(__props, { expose: __expose }) {
      __expose();
      const store = userInfoStore$1();
      const {
        user,
        roleID,
        teamID
      } = (0, import_pinia2.storeToRefs)(store);
      const current = (0, import_vue2.ref)(0);
      const tabs = ["\u5927\u5587\u53ED", "\u5C0F\u5587\u53ED"];
      let listSmall = (0, import_vue2.ref)([]);
      let listBig = (0, import_vue2.ref)([]);
      const list = (0, import_vue2.ref)([
        {
          type: "video",
          topTip: "\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",
          poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
          src: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
          bottomTip: ""
          //'这是一个小喇叭----底部提示',
        },
        {
          type: "image",
          topTip: "\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",
          src: "https://img2.baidu.com/it/u=3256616248,1972425356&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=1039"
        },
        {
          type: "image",
          topTip: "\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",
          src: "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201707%2F23%2F20170723111737_JZGmC.thumb.700_0.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1725513912&t=01acc1e9efcbfacd570fed69edd5a9aa"
        },
        {
          type: "video",
          topTip: "\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",
          currentTime: 120,
          //初始帧时间---默认缓存存储
          poster: "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fss2.meipian.me%2Fusers%2F3107464%2F269b3689a58b4e1f8eb7a882eae338fb.jpg%3Fmeipian-raw%2Fbucket%2Fivwen%2Fkey%2FdXNlcnMvMzEwNzQ2NC8yNjliMzY4OWE1OGI0ZTFmOGViN2E4ODJlYWUzMzhmYi5qcGc%3D%2Fsign%2F203e153600702e8c1f73d97470fd9234.jpg&refer=http%3A%2F%2Fss2.meipian.me&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1725513912&t=158a229b97ec18cbcc9a07aabc00e685",
          src: "https://www.w3schools.com/html/movie.mp4"
        }
      ]);
      const ad = (0, import_vue2.ref)([]);
      let styleName = (0, import_vue2.ref)("");
      const groupList = (0, import_vue2.ref)([]);
      const win = uni.getSystemInfoSync();
      const width = win.windowWidth;
      const height = win.windowHeight;
      const bottomStyle = {
        // "position": "absolute",
        "top": "36px"
        // "left": "0",
        // "display": "flex",
        // "flex-wrap": "wrap",
        // "flex-direction": "column"
      };
      const realList = (0, import_vue2.ref)([]);
      const currentVedio = (0, import_vue2.ref)(0);
      let context = null;
      const counter = (0, import_vue2.ref)(0);
      let windowWidth = 0;
      let statusBarHeight = 0;
      const onchange = (index, size) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:195", "onchange-\u5F53\u524D\u7D22\u5F15:", index + "aa" + size);
        currentVedio.value = index;
        if (index == 0) {
          uni.showToast({ title: "\u5F53\u524D\u5DF2\u662F\u7B2C\u4E00\u4E2A\u89C6\u9891", icon: "none", mask: false });
        }
        if (index == size - 1) {
          uni.showToast({ title: "\u5F53\u524D\u5DF2\u662F\u6700\u540E\u4E00\u4E2A\u89C6\u9891", icon: "none", mask: false });
        }
      };
      const loadMore = (index, size) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:207", "\u52A0\u8F7D\u66F4\u6240\u89C6\u9891\uFF1A", index + " / " + size);
        if (counter.value > 5)
          return;
        getList().forEach((item) => {
          item.title = realList.value.length + "\uFF0C" + item.title + item.title + item.title;
          realList.value.push(item);
        });
        counter.value = counter.value + 1;
      };
      const play = (context2) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:221", "\u89C6\u9891\u5F00\u59CB\u64AD\u653E");
      };
      const pause = (context2) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:228", "\u89C6\u9891\u6682\u505C\u64AD\u653E");
      };
      const ended = (context2) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:235", "\u89C6\u9891\u64AD\u653E\u7ED3\u675F");
      };
      const error = (context2, event) => {
        formatAppLog("error", "at pagesSub/pages/adv/buyList.nvue:242", "\u89C6\u9891\u64AD\u653E\u51FA\u9519\uFF1A", event);
      };
      const waiting = (context2) => {
        formatAppLog("error", "at pagesSub/pages/adv/buyList.nvue:249", "\u89C6\u9891\u51FA\u73B0\u7F13\u51B2");
      };
      const videoClick = (context2, video) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:256", "\u70B9\u51FB\u4E86\u89C6\u9891\uFF1A", video);
      };
      const doubleClick = (context2, video) => {
        phone.value = video.author.tel;
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:264", "\u53CC\u51FB\u4E86\u89C6\u9891\uFF1A", video);
        uni.showModal({
          title: "\u63D0\u793A",
          content: "\u62E8\u6253\u7535\u8BDD\u7ED9\u53D1\u5E03\u4EBA\uFF1F",
          confirmText: "\u62E8\u6253",
          cancelText: "\u53D6\u6D88",
          success: function(res) {
            if (res.confirm) {
              uni.makePhoneCall({
                phoneNumber: phone.value
              });
            } else if (res.cancel) {
              formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:292", "\u7528\u6237\u70B9\u51FB\u53D6\u6D88");
            }
          }
        });
      };
      const maskClick = (index, video) => {
        context = context;
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:301", "\u70B9\u51FB\u4E86\u8499\u5C42\uFF1A", index, video);
      };
      const getList = () => {
        return [{
          videoId: realList.value.length + 1,
          title: "\u3002",
          poster: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
          url: "https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",
          uploadTime: "2023-11-08 19:41",
          ipLocation: "\u4E0A\u6D77",
          author: {
            authorId: 101,
            avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
            nickName: "\u964C\u8DEF",
            genderName: "\u7537"
          }
        }];
      };
      const changeEvent = (e) => {
        current.value = e;
      };
      onShow(() => {
        let userInfo = (0, import_vue2.toRaw)(user.value);
        if (userInfo.ID == "") {
          store.clear();
          uni.reLaunch({
            url: "/pagesSub/pages/login/login"
          });
        }
        if (roleID.value == "") {
          uni.reLaunch({
            url: "/pages/my/my"
          });
        }
      });
      onLoad((e) => {
        getAdv();
      });
      onNavigationBarButtonTap(() => {
        uni.navigateTo({
          url: "/pagesSub/pages/message/message"
        });
      });
      (0, import_vue2.onMounted)(() => {
        getAdv();
      });
      const handleAdClick = (position) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:354", "handleAdClick " + position);
      };
      const changeTab = (index) => {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:360", index);
      };
      const resetEvent = () => {
        styleName.value = "";
        getGroup();
      };
      const searchEvent = () => {
        getGroup();
      };
      const publishEvent = () => {
        uni.removeStorageSync("storage_orderinfo");
        if (current.value == 0) {
          uni.navigateTo({
            url: "/pagesSub/pages/adv/publishFH"
          });
        }
        if (current.value == 1) {
          uni.navigateTo({
            url: "/pagesSub/pages/adv/publishZG"
          });
        }
        if (current.value == 2) {
          uni.navigateTo({
            url: "/pagesSub/pages/adv/publishSH"
          });
        }
      };
      const getGroup = () => __async(this, null, function* () {
        let r = yield apis.Chat.GroupList({
          groupId: "",
          groupName: styleName.value
        });
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:396", r);
        groupList.value.splice(0);
        if (r.ResData.length > 0) {
          r.ResData.forEach(function(item, index) {
            groupList.value.push(item);
          });
        }
      });
      const getAdv = () => __async(this, null, function* () {
        listBig.value.splice(0);
        listSmall.value.splice(0);
        let userInfo = (0, import_vue2.toRaw)(user.value);
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:410", userInfo.ID);
        apis.Adv.BuyList({
          userid: userInfo.ID.toString()
        }).then(function(res) {
          formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:414", res);
          if (res.Success && res.ResData.length > 0) {
            res.ResData.forEach(function(item, index) {
              if (item.category == "small") {
                if (item.type == "SH") {
                  listSmall.value.push({
                    videoId: listSmall.value.length + 1,
                    title: "\u63A5\u8D27\u6D88\u606F:\n	\u5730\u5740:" + item.sh_address + "\n	\u59D3\u540D:" + item.sh_name + "\n	\u8054\u7CFB\u7535\u8BDD:" + item.sh_tel + "\n	\u4EBA\u6570\u89C4\u6A21:" + item.sh_gm + "\u4EBA \n	\u64C5\u957F\u6B3E\u5F0F:" + item.sh_style + "\n	\u5305\u6599\u80FD\u529B:" + item.sh_blnl + "\n	\u6253\u7248\u5F00\u53D1\u80FD\u529B:" + item.sh_dbkfnl + "\n	\u52A0\u5DE5\u96BE\u5EA6:" + item.sh_jgnd + "\n	\n	\u64AD\u653E\u6B21\u6570:99\u6B21\n	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*\n	",
                    poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                    //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    url: apis.Url.baseUrlBiz + "/" + item.sh_mat_path,
                    uploadTime: "2024-10-02 09:41",
                    ipLocation: "\u5317\u4EAC",
                    author: {
                      authorId: 102,
                      avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                      nickName: "\u7BA1\u7406\u5458",
                      genderName: "\u5973",
                      tel: item.sh_tel
                    }
                  });
                }
                if (item.type == "FH") {
                  listSmall.value.push({
                    videoId: listSmall.value.length + 1,
                    title: "\u53D1\u8D27\u6D88\u606F:\n	\u8054\u7CFB\u7535\u8BDD:" + item.fh_Tel + "\n	\u671F\u671B\u52A0\u5DE5\u5730\u5740:" + item.fh_address + "\n	\u5DE5\u671F\u9650\u5236:" + item.fh_limitGQ + "\n	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:" + item.fh_fzType + "\n	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:" + item.fh_JBGYBZ + "\n	\u8D26\u671F\u671F\u671B:" + item.fh_ZQQW + "\n	\u8BA2\u5355\u6570\u91CF:" + item.fh_orderNum + "\n	\u662F\u5426\u5305\u88C1:" + item.fh_SFBC + "\n	\u662F\u5426\u5305\u9762\u8F85\u6599:" + item.fh_SFMLFZ + "\n	\n	\u64AD\u653E\u6B21\u6570:99\u6B21\n	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*\n	",
                    poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                    //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    url: apis.Url.baseUrlBiz + "/" + item.fh_mat_path,
                    uploadTime: "2024-10-02 09:41",
                    ipLocation: "\u5317\u4EAC",
                    author: {
                      authorId: 102,
                      avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                      nickName: "\u7BA1\u7406\u5458",
                      genderName: "\u5973",
                      tel: item.fh_Tel
                    }
                  });
                }
                if (item.type == "ZG") {
                  var zhaopinList = JSON.parse(item.zg_zaopinList);
                  var gwList = "";
                  zhaopinList.forEach(function(item1, index2) {
                    gwList += item1.type + "  \u62DB\u8058\u4EBA\u6570:" + item1.quantity + "\u4EBA\n	";
                  });
                  listSmall.value.push({
                    videoId: listSmall.value.length + 1,
                    title: "\u62DB\u8058\u901A\u77E5:\n	\u5730\u5740:" + item.zg_address + "\n	\u59D3\u540D:" + item.zg_name + "\n	\u8054\u7CFB\u7535\u8BDD:" + item.zg_tel + "\n	\u62DB\u8058\u4FE1\u606F:\n	" + gwList + "\n	\u64AD\u653E\u6B21\u6570:99\u6B21\n	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*\n	",
                    poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                    //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    url: apis.Url.baseUrlBiz + "/" + item.zg_mat_path,
                    uploadTime: "2024-10-02 09:41",
                    ipLocation: "\u5317\u4EAC",
                    author: {
                      authorId: 102,
                      avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                      nickName: "\u7BA1\u7406\u5458",
                      genderName: "\u5973",
                      tel: item.zg_tel
                    }
                  });
                }
              }
              if (item.category == "big") {
                if (item.type == "SH") {
                  listBig.value.push({
                    videoId: listBig.value.length + 1,
                    title: "\u63A5\u8D27\u6D88\u606F:\n	\u5730\u5740:" + item.sh_address + "\n	\u59D3\u540D:" + item.sh_name + "\n	\u8054\u7CFB\u7535\u8BDD:" + item.sh_tel + "\n	\u4EBA\u6570\u89C4\u6A21:" + item.sh_gm + "\u4EBA \n	\u64C5\u957F\u6B3E\u5F0F:" + item.sh_style + "\n	\u5305\u6599\u80FD\u529B:" + item.sh_blnl + "\n	\u6253\u7248\u5F00\u53D1\u80FD\u529B:" + item.sh_dbkfnl + "\n	\u52A0\u5DE5\u96BE\u5EA6:" + item.sh_jgnd + "\n	\n	\u64AD\u653E\u6B21\u6570:99\u6B21\n	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*\n	",
                    poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                    //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    url: apis.Url.baseUrlBiz + "/" + item.sh_mat_path,
                    uploadTime: "2024-10-02 09:41",
                    ipLocation: "\u5317\u4EAC",
                    author: {
                      authorId: 102,
                      avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                      nickName: "\u7BA1\u7406\u5458",
                      genderName: "\u5973",
                      tel: item.sh_tel
                    }
                  });
                }
                if (item.type == "FH") {
                  listBig.value.push({
                    videoId: listBig.value.length + 1,
                    title: "\u53D1\u8D27\u6D88\u606F:\n	\u8054\u7CFB\u7535\u8BDD:" + item.fh_Tel + "\n	\u671F\u671B\u52A0\u5DE5\u5730\u5740:" + item.fh_address + "\n	\u5DE5\u671F\u9650\u5236:" + item.fh_limitGQ + "\n	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:" + item.fh_fzType + "\n	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:" + item.fh_JBGYBZ + "\n	\u8D26\u671F\u671F\u671B:" + item.fh_ZQQW + "\n	\u8BA2\u5355\u6570\u91CF:" + item.fh_orderNum + "\n	\u662F\u5426\u5305\u88C1:" + item.fh_SFBC + "\n	\u662F\u5426\u5305\u9762\u8F85\u6599:" + item.fh_SFMLFZ + "\n	\n	\u64AD\u653E\u6B21\u6570:99\u6B21\n	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*\n	",
                    poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                    //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    url: apis.Url.baseUrlBiz + "/" + item.fh_mat_path,
                    uploadTime: "2024-10-02 09:41",
                    ipLocation: "\u5317\u4EAC",
                    author: {
                      authorId: 102,
                      avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                      nickName: "\u7BA1\u7406\u5458",
                      genderName: "\u5973",
                      tel: item.fh_Tel
                    }
                  });
                }
                if (item.type == "ZG") {
                  var zhaopinList = JSON.parse(item.zg_zaopinList);
                  var gwList = "";
                  zhaopinList.forEach(function(item1, index2) {
                    gwList += item1.type + "  \u62DB\u8058\u4EBA\u6570:" + item1.quantity + "\u4EBA\n	";
                  });
                  listBig.value.push({
                    videoId: listBig.value.length + 1,
                    title: "\u62DB\u8058\u901A\u77E5:\n	\u5730\u5740:" + item.zg_address + "\n	\u59D3\u540D:" + item.zg_name + "\n	\u8054\u7CFB\u7535\u8BDD:" + item.zg_tel + "\n	\u62DB\u8058\u4FE1\u606F:\n	" + gwList + "\n	\u64AD\u653E\u6B21\u6570:99\u6B21\n	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*\n	",
                    poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                    //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    url: apis.Url.baseUrlBiz + "/" + item.zg_mat_path,
                    uploadTime: "2024-10-02 09:41",
                    ipLocation: "\u5317\u4EAC",
                    author: {
                      authorId: 102,
                      avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                      nickName: "\u7BA1\u7406\u5458",
                      genderName: "\u5973",
                      tel: item.zg_tel
                    }
                  });
                }
              }
            });
          }
        }.bind(this));
      });
      const __returned__ = { store, user, roleID, teamID, current, tabs, effect3d, effect3dMargin, autoplay, vertical, fullScreen, topFloat, fotterFloat, mode, indicatorPos, get listSmall() {
        return listSmall;
      }, set listSmall(v) {
        listSmall = v;
      }, get listBig() {
        return listBig;
      }, set listBig(v) {
        listBig = v;
      }, list, dotIndex, dotFloatIndex, ad, get styleName() {
        return styleName;
      }, set styleName(v) {
        styleName = v;
      }, groupList, win, width, height, bottomStyle, realList, currentVedio, get context() {
        return context;
      }, set context(v) {
        context = v;
      }, counter, get windowWidth() {
        return windowWidth;
      }, set windowWidth(v) {
        windowWidth = v;
      }, get statusBarHeight() {
        return statusBarHeight;
      }, set statusBarHeight(v) {
        statusBarHeight = v;
      }, onchange, loadMore, play, pause, ended, error, waiting, videoClick, doubleClick, maskClick, getList, changeEvent, handleAdClick, changeTab, resetEvent, searchEvent, publishEvent, getGroup, getAdv, ref: import_vue2.ref, toRaw: import_vue2.toRaw, onMounted: import_vue2.onMounted, get onLoad() {
        return onLoad;
      }, get onShow() {
        return onShow;
      }, get onNavigationBarButtonTap() {
        return onNavigationBarButtonTap;
      }, get Https() {
        return apis;
      }, get userInfoStore() {
        return userInfoStore$1;
      }, get storeToRefs() {
        return import_pinia2.storeToRefs;
      } };
      Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
      return __returned__;
    }
  };
  function _sfc_render2(_ctx, _cache, $props, $setup, $data, $options) {
    const _component_ml_swiper = resolveEasycom((0, import_vue2.resolveDynamicComponent)("ml-swiper"), __easycom_0);
    return (0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("scroll-view", {
      scrollY: true,
      showScrollbar: true,
      enableBackToTop: true,
      bubble: "true",
      style: { flexDirection: "column" }
    }, [
      (0, import_vue2.createCommentVNode)(' <v-tabs v-model="current" :scroll="false" :tabs="tabs"></v-tabs> '),
      (0, import_vue2.createCommentVNode)(' <v-tabs v-model="current" :tabs="tabs" :pills="true" line-height="0" pillsColor="#007aff" activeColor="#fff" @change="changeTab"></v-tabs> '),
      (0, import_vue2.createElementVNode)("view", { class: "tabsviewContent" }, [
        (0, import_vue2.createCommentVNode)(` <v-tabs class="tabsview" v-model="current" :scroll="false" \r
		:tabs="tabs" :bgColor='bgColor' :lineColor="lineColor"\r
		@change="changeEvent"></v-tabs> `),
        (0, import_vue2.createElementVNode)("view", { class: "top_nav" }, [
          (0, import_vue2.createElementVNode)(
            "view",
            {
              style: (0, import_vue2.normalizeStyle)({ height: $setup.statusBarHeight })
            },
            null,
            4
            /* STYLE */
          ),
          (0, import_vue2.createElementVNode)("view", { class: "top_content" }, [
            (0, import_vue2.createElementVNode)("view", { class: "content_btn" }, [
              (0, import_vue2.createElementVNode)("view", {
                class: "content_item",
                onClick: _cache[0] || (_cache[0] = ($event) => $setup.changeEvent(0))
              }, [
                (0, import_vue2.createElementVNode)(
                  "u-text",
                  {
                    class: (0, import_vue2.normalizeClass)(["item_title", { "i_on": $setup.current === 0 }])
                  },
                  "\u5927\u5587\u53ED",
                  2
                  /* CLASS */
                ),
                $setup.current == 0 ? ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("view", {
                  key: 0,
                  class: "line_on"
                })) : ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("view", { key: 1 }))
              ]),
              (0, import_vue2.createElementVNode)("view", {
                class: "content_item",
                onClick: _cache[1] || (_cache[1] = ($event) => $setup.changeEvent(1))
              }, [
                (0, import_vue2.createElementVNode)(
                  "u-text",
                  {
                    class: (0, import_vue2.normalizeClass)(["item_title", { "i_on": $setup.current === 1 }])
                  },
                  "\u5C0F\u5587\u53ED",
                  2
                  /* CLASS */
                ),
                $setup.current == 1 ? ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("view", {
                  key: 0,
                  class: "line_on"
                })) : ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("view", { key: 1 }))
              ])
            ])
          ])
        ])
      ]),
      $setup.current == 0 ? ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("view", {
        key: 0,
        class: "work_1"
      }, [
        (0, import_vue2.createCommentVNode)(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listBig'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			></zSwiper> "),
        (0, import_vue2.createVNode)(_component_ml_swiper, {
          videoList: $setup.listBig,
          width: $setup.width,
          height: $setup.height,
          bottomStyle: $setup.bottomStyle,
          onLoadMore: $setup.loadMore,
          onChange: $setup.onchange,
          onPlay: $setup.play,
          onPause: $setup.pause,
          onEnded: $setup.ended,
          onError: $setup.error,
          onWaiting: $setup.waiting,
          onVideoClick: $setup.videoClick,
          onDoubleClick: $setup.doubleClick,
          onMaskClick: $setup.maskClick
        }, {
          bottom: (0, import_vue2.withCtx)(({ video, index }) => [
            (0, import_vue2.createCommentVNode)(" \u89C6\u9891\u6807\u9898 "),
            video ? ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)(
              "u-text",
              {
                key: 0,
                class: "videoTitle"
              },
              (0, import_vue2.toDisplayString)(video == null ? void 0 : video.title),
              1
              /* TEXT */
            )) : (0, import_vue2.createCommentVNode)("v-if", true)
          ]),
          _: 1
          /* STABLE */
        }, 8, ["videoList", "width", "height"])
      ])) : (0, import_vue2.createCommentVNode)("v-if", true),
      $setup.current == 1 ? ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)("view", {
        key: 1,
        class: "work_1"
      }, [
        (0, import_vue2.createCommentVNode)(' <image class="imgV" src="https://img1.baidu.com/it/u=4232351783,2577170786&fm=253&fmt=auto?w=333&h=500"></image> '),
        (0, import_vue2.createCommentVNode)(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listSmall'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			></zSwiper> "),
        (0, import_vue2.createVNode)(_component_ml_swiper, {
          videoList: $setup.listSmall,
          width: $setup.width,
          height: $setup.height,
          bottomStyle: $setup.bottomStyle,
          onLoadMore: $setup.loadMore,
          onChange: $setup.onchange,
          onPlay: $setup.play,
          onPause: $setup.pause,
          onEnded: $setup.ended,
          onError: $setup.error,
          onWaiting: $setup.waiting,
          onVideoClick: $setup.videoClick,
          onDoubleClick: $setup.doubleClick,
          onMaskClick: $setup.maskClick
        }, {
          bottom: (0, import_vue2.withCtx)(({ video, index }) => [
            (0, import_vue2.createCommentVNode)(" \u89C6\u9891\u6807\u9898 "),
            video ? ((0, import_vue2.openBlock)(), (0, import_vue2.createElementBlock)(
              "u-text",
              {
                key: 0,
                class: "videoTitle"
              },
              (0, import_vue2.toDisplayString)(video == null ? void 0 : video.title),
              1
              /* TEXT */
            )) : (0, import_vue2.createCommentVNode)("v-if", true)
          ]),
          _: 1
          /* STABLE */
        }, 8, ["videoList", "width", "height"])
      ])) : (0, import_vue2.createCommentVNode)("v-if", true)
    ]);
  }
  var buyList = /* @__PURE__ */ _export_sfc(_sfc_main2, [["render", _sfc_render2], ["styles", [_style_02]], ["__file", "E:/\u5F00\u53D1\u6240\u9700/\u777F\u777F\u6E90\u7801/App/pagesSub/pages/adv/buyList.nvue"]]);

  // <stdin>
  var webview = plus.webview.currentWebview();
  if (webview) {
    const __pageId = parseInt(webview.id);
    const __pagePath = "pagesSub/pages/adv/buyList";
    let __pageQuery = {};
    try {
      __pageQuery = JSON.parse(webview.__query__);
    } catch (e) {
    }
    buyList.mpType = "page";
    const app = Vue.createPageApp(buyList, { $store: getApp({ allowDefault: true }).$store, __pageId, __pagePath, __pageQuery });
    app.provide("__globalStyles", Vue.useCssStyles([...__uniConfig.styles, ...buyList.styles || []]));
    app.mount("#root");
  }
})();
