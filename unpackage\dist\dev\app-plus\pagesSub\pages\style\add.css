/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more[data-v-9245e42c] {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text[data-v-9245e42c] {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img[data-v-9245e42c] {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue[data-v-9245e42c] {
  color: #666666;
}
.uni-load-more__img--android[data-v-9245e42c],
.uni-load-more__img--ios[data-v-9245e42c] {
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
}
.uni-load-more__img--android[data-v-9245e42c] {
  animation: loading-ios 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5[data-v-9245e42c] {
  position: relative;
  animation: loading-ios-H5-9245e42c 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 uni-image[data-v-9245e42c] {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@keyframes loading-ios-H5-9245e42c {
0% {
    transform: rotate(0deg);
}
8% {
    transform: rotate(30deg);
}
16% {
    transform: rotate(60deg);
}
24% {
    transform: rotate(90deg);
}
32% {
    transform: rotate(120deg);
}
40% {
    transform: rotate(150deg);
}
48% {
    transform: rotate(180deg);
}
56% {
    transform: rotate(210deg);
}
64% {
    transform: rotate(240deg);
}
73% {
    transform: rotate(270deg);
}
82% {
    transform: rotate(300deg);
}
91% {
    transform: rotate(330deg);
}
100% {
    transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP[data-v-9245e42c] {
  position: relative;
  width: 24px;
  height: 24px;
  transform: rotate(0deg);
  animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c] {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(1) {
  animation: loading-android-MP-1-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(2) {
  animation: loading-android-MP-2-9245e42c 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon[data-v-9245e42c]:nth-child(3) {
  animation: loading-android-MP-3-9245e42c 1s 0s linear infinite;
}
@keyframes loading-android-9245e42c {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(90deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(180deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3-9245e42c {
0% {
    transform: rotate(0deg);
}
50% {
    transform: rotate(270deg);
}
100% {
    transform: rotate(360deg);
}
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-data-loading[data-v-5e619820] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 36px;
  padding-left: 10px;
  color: #999;
}
.uni-data-checklist[data-v-5e619820] {
  position: relative;
  z-index: 0;
  flex: 1;
}
.uni-data-checklist .checklist-group[data-v-5e619820] {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
}
.uni-data-checklist .checklist-group.is-list[data-v-5e619820] {
  flex-direction: column;
}
.uni-data-checklist .checklist-group .checklist-box[data-v-5e619820] {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  margin: 5px 0;
  margin-right: 25px;
}
.uni-data-checklist .checklist-group .checklist-box .hidden[data-v-5e619820] {
  position: absolute;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content[data-v-5e619820] {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checklist-text[data-v-5e619820] {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
  line-height: 14px;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checkobx__list[data-v-5e619820] {
  border-right-width: 1px;
  border-right-color: #007aff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #007aff;
  border-bottom-style: solid;
  height: 12px;
  width: 6px;
  left: -5px;
  transform-origin: center;
  transform: rotate(45deg);
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner[data-v-5e619820] {
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner .checkbox__inner-icon[data-v-5e619820] {
  position: absolute;
  top: 1px;
  left: 5px;
  height: 8px;
  width: 4px;
  border-right-width: 1px;
  border-right-color: #fff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #fff;
  border-bottom-style: solid;
  opacity: 0;
  transform-origin: center;
  transform: rotate(40deg);
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner[data-v-5e619820] {
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 16px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner .radio__inner-icon[data-v-5e619820] {
  width: 8px;
  height: 8px;
  border-radius: 10px;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checkbox__inner[data-v-5e619820] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .radio__inner[data-v-5e619820] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checklist-text[data-v-5e619820] {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner[data-v-5e619820] {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner .checkbox__inner-icon[data-v-5e619820] {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner[data-v-5e619820] {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner .radio__inner-icon[data-v-5e619820] {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checklist-text[data-v-5e619820] {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checkbox__inner[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checklist-text[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .radio__inner[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button[data-v-5e619820] {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  transition: border-color 0.2s;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable[data-v-5e619820] {
  border: 1px #eee solid;
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checkbox__inner[data-v-5e619820] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .radio__inner[data-v-5e619820] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checklist-text[data-v-5e619820] {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked[data-v-5e619820] {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner[data-v-5e619820] {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner .checkbox__inner-icon[data-v-5e619820] {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner[data-v-5e619820] {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner .radio__inner-icon[data-v-5e619820] {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checklist-text[data-v-5e619820] {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked.is-disable[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag[data-v-5e619820] {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  background-color: #f5f5f5;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag .checklist-text[data-v-5e619820] {
  margin: 0;
  color: #666;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-disable[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked[data-v-5e619820] {
  background-color: #2979ff;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked .checklist-text[data-v-5e619820] {
  color: #fff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list[data-v-5e619820] {
  display: flex;
  padding: 10px 15px;
  padding-left: 0;
  margin: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-list-border[data-v-5e619820] {
  border-top: 1px #eee solid;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checkbox__inner[data-v-5e619820] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checklist-text[data-v-5e619820] {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner[data-v-5e619820] {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner .checkbox__inner-icon[data-v-5e619820] {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .radio__inner .radio__inner-icon[data-v-5e619820] {
  opacity: 1;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-text[data-v-5e619820] {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-content .checkobx__list[data-v-5e619820] {
  opacity: 1;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checkbox__inner[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checklist-text[data-v-5e619820] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag[data-v-5e619820] {
  padding: 5px 5px !important;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag[data-v-5e619820] {
  padding: 5px 3px !important;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.empty[data-v-e2280098] {
  display: grid;
  place-items: center;
  min-height: 18.75rem;
  color: #999;
  font-size: 1rem;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.modal[data-v-4bcd74eb] {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  outline: 0;
  text-align: center;
  transform: scale(1.185);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  perspective: 62.5rem;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}
.modal[data-v-4bcd74eb]::before {
  content: "​";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.modal.show[data-v-4bcd74eb] {
  opacity: 1;
  transition-duration: 0.3s;
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}
.modal .dialog[data-v-4bcd74eb] {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 21.25rem;
  max-width: 100%;
  background-color: #f8f8f8;
  border-radius: 0.3125rem;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
}
.modal.bottom[data-v-4bcd74eb] {
  margin-bottom: -31.25rem;
}
.modal.bottom[data-v-4bcd74eb]::before {
  vertical-align: bottom;
}
.modal.bottom.show[data-v-4bcd74eb] {
  margin-bottom: 0;
}
.modal.bottom .dialog[data-v-4bcd74eb] {
  width: 100%;
  border-radius: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-data-loading[data-v-2f788efd] {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 36px;
  padding-left: 10px;
  color: #999;
}
.uni-data-checklist[data-v-2f788efd] {
  position: relative;
  z-index: 0;
  flex: 1;
}
.uni-data-checklist .checklist-group[data-v-2f788efd] {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
}
.uni-data-checklist .checklist-group.is-list[data-v-2f788efd] {
  flex-direction: column;
}
.uni-data-checklist .checklist-group .checklist-box[data-v-2f788efd] {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  margin: 5px 0;
  margin-right: 25px;
}
.uni-data-checklist .checklist-group .checklist-box .hidden[data-v-2f788efd] {
  position: absolute;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content[data-v-2f788efd] {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checklist-text[data-v-2f788efd] {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
  line-height: 14px;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checkobx__list[data-v-2f788efd] {
  border-right-width: 1px;
  border-right-color: #007aff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #007aff;
  border-bottom-style: solid;
  height: 12px;
  width: 6px;
  left: -5px;
  transform-origin: center;
  transform: rotate(45deg);
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner[data-v-2f788efd] {
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner .checkbox__inner-icon[data-v-2f788efd] {
  position: absolute;
  top: 1px;
  left: 5px;
  height: 8px;
  width: 4px;
  border-right-width: 1px;
  border-right-color: #fff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #fff;
  border-bottom-style: solid;
  opacity: 0;
  transform-origin: center;
  transform: rotate(40deg);
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner[data-v-2f788efd] {
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 16px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner .radio__inner-icon[data-v-2f788efd] {
  width: 8px;
  height: 8px;
  border-radius: 10px;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checkbox__inner[data-v-2f788efd] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .radio__inner[data-v-2f788efd] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checklist-text[data-v-2f788efd] {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner[data-v-2f788efd] {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner .checkbox__inner-icon[data-v-2f788efd] {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner[data-v-2f788efd] {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner .radio__inner-icon[data-v-2f788efd] {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checklist-text[data-v-2f788efd] {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checkbox__inner[data-v-2f788efd] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checklist-text[data-v-2f788efd] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .radio__inner[data-v-2f788efd] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button[data-v-2f788efd] {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  transition: border-color 0.2s;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable[data-v-2f788efd] {
  border: 1px #eee solid;
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checkbox__inner[data-v-2f788efd] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .radio__inner[data-v-2f788efd] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checklist-text[data-v-2f788efd] {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked[data-v-2f788efd] {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner[data-v-2f788efd] {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner .checkbox__inner-icon[data-v-2f788efd] {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner[data-v-2f788efd] {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner .radio__inner-icon[data-v-2f788efd] {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checklist-text[data-v-2f788efd] {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked.is-disable[data-v-2f788efd] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag[data-v-2f788efd] {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  background-color: #f5f5f5;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag .checklist-text[data-v-2f788efd] {
  margin: 0;
  color: #666;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-disable[data-v-2f788efd] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked[data-v-2f788efd] {
  background-color: #2979ff;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked .checklist-text[data-v-2f788efd] {
  color: #fff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list[data-v-2f788efd] {
  display: flex;
  padding: 10px 15px;
  padding-left: 0;
  margin: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-list-border[data-v-2f788efd] {
  border-top: 1px #eee solid;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checkbox__inner[data-v-2f788efd] {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checklist-text[data-v-2f788efd] {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner[data-v-2f788efd] {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner .checkbox__inner-icon[data-v-2f788efd] {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .radio__inner .radio__inner-icon[data-v-2f788efd] {
  opacity: 1;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-text[data-v-2f788efd] {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-content .checkobx__list[data-v-2f788efd] {
  opacity: 1;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checkbox__inner[data-v-2f788efd] {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checklist-text[data-v-2f788efd] {
  opacity: 0.4;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.btnLook[data-v-bc3afb76] {
  width: 5rem;
  height: 1.875rem;
  font-size: 11px;
  color: white;
  background-color: #007aff;
  line-height: 1.875rem;
}
.detail[data-v-bc3afb76] {
  max-height: 60vh;
  padding: 0 0.625rem 0 0.625rem;
  background-color: #ffffff;
}
.detail .title[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.5rem;
  padding: 0 0;
  border-bottom: 0.03125rem solid #e5e7eb;
  font-weight: bold;
}
.detail .title .status[data-v-bc3afb76] {
  font-size: 0.8125rem;
  font-weight: normal;
}
.detail .title .status.success[data-v-bc3afb76] {
  color: #007aff;
}
.detail .title .status.fail[data-v-bc3afb76] {
  color: #dd524d;
}
.detail .cont .jobs[data-v-bc3afb76] {
  font-size: 0.875rem;
}
.detail .cont .jobs .scrollView[data-v-bc3afb76] {
  max-height: 79vh;
  padding: 0.625rem 0.625rem 0.625rem 0.625rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail .cont .jobs .row[data-v-bc3afb76] {
  padding: 0.625rem 0.625rem 0.625rem 0.625rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail .cont .jobs .row[data-v-bc3afb76]:nth-child(odd) {
  background-color: #f5f9ff;
}
.detail .cont .jobs .row .cell[data-v-bc3afb76]:nth-child(1) {
  width: 5.625rem;
}
.detail .cont .jobs .row .cell[data-v-bc3afb76]:nth-child(2) {
  flex: 1;
}
.detail .cont .jobs .row .cell .item[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail .cont .jobs .row .cell .item .child[data-v-bc3afb76] {
  padding: 0.3125rem 0.1875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.detail .cont .jobs .row .cell .item .child[data-v-bc3afb76]:nth-child(1), .detail .cont .jobs .row .cell .item .child[data-v-bc3afb76]:nth-child(2) {
  width: 3.125rem;
}
.detail .cont .jobs .row .cell .item .child[data-v-bc3afb76]:nth-child(3) {
  flex: 1;
}
.detail .cont .jobs .row .cell .item .child .name[data-v-bc3afb76] {
  flex: 1;
}
.detail .cont .jobs .row .cell .item .child .name .primary[data-v-bc3afb76] {
  color: #007aff;
}
uni-scroll-view[data-v-bc3afb76]::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  border-radius: 0;
  background-color: transparent;
  color: transparent;
}

/***裁剪单***/
.cropping[data-v-bc3afb76] {
  padding: 0.625rem;
}
.cropping .title[data-v-bc3afb76] {
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.5rem;
  padding: 0 0;
  border-bottom: 0.03125rem solid #e5e7eb;
  font-weight: bold;
}
.cropping .title .status[data-v-bc3afb76] {
  font-size: 0.8125rem;
  font-weight: normal;
}
.cropping .title .status.success[data-v-bc3afb76] {
  color: #007aff;
}
.cropping .title .status.fail[data-v-bc3afb76] {
  color: #dd524d;
}
.cropping .title .btn[data-v-bc3afb76] {
  width: 3.75rem;
  padding: 0;
}
.cropping .btnGroup[data-v-bc3afb76] {
  display: flex;
  align-items: center;
}
.cropping .btnGroup .addLink[data-v-bc3afb76] {
  width: 3.4375rem;
  margin: 0.25rem;
  padding: 0.03125rem !important;
}
.cropping .btnGroup .addLinkCJD[data-v-bc3afb76] {
  width: 4.375rem;
  margin: 0.25rem;
  padding: 0.03125rem !important;
}
.cropping .style[data-v-bc3afb76] {
  background-color: #ffffff;
  padding: 0.625rem;
  border-radius: 6px;
  font-size: 0.875rem;
}
.cropping .style-list .item[data-v-bc3afb76] {
  margin-top: 0.625rem;
}
.cropping .style-list .item .input[data-v-bc3afb76] {
  border-radius: 6px;
  font-size: 0.875rem;
  height: 2rem;
  padding: 0 0.625rem;
  background-color: #f8f8f8;
}
.cropping .style-list .item .input .value[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 1.875rem;
}
.cropping .style-list .item .input .value[data-v-bc3afb76]::after {
  font-family: "iconfont";
  content: "\e840";
  color: #999;
}
.cropping .style-list .item .input .value[data-v-bc3afb76]:empty::before {
  content: attr(data-tips);
  color: gray;
}
.cropping .size[data-v-bc3afb76] {
  background-color: #ffffff;
  padding: 0.625rem;
  margin-top: 0.625rem;
  border-radius: 6px;
  font-size: 0.875rem;
}
.cropping .size-list[data-v-bc3afb76] {
  margin-top: 0.625rem;
  display: grid;
  grid-template-columns: 0.2fr 0.3fr 1fr 1fr 2rem;
  gap: 0.625rem;
}
.cropping .size-list .input[data-v-bc3afb76] {
  border-radius: 6px;
  font-size: 0.875rem;
  height: 2rem;
  padding: 0 0.625rem;
  background-color: #f8f8f8;
}
.cropping .size-list .checkbox[data-v-bc3afb76] {
  border-radius: 6px;
  font-size: 0.875rem;
  height: 2rem;
  padding: 0.3125rem 0;
}
.cropping .size-list1[data-v-bc3afb76] {
  margin-top: 0.625rem;
  display: grid;
  grid-template-columns: 1fr 1fr 2rem;
  gap: 0.625rem;
}
.cropping .size-list1 .input[data-v-bc3afb76] {
  border-radius: 6px;
  font-size: 0.875rem;
  height: 2rem;
  padding: 0 0.625rem;
  background-color: #f8f8f8;
}
.cropping .size-list1 .checkbox[data-v-bc3afb76] {
  border-radius: 6px;
  font-size: 0.875rem;
  height: 2rem;
  padding: 0.3125rem 0;
}
.cropping .size .add[data-v-bc3afb76] {
  display: flex;
  margin-top: 0.625rem;
}
.cropping .size .sizeContent[data-v-bc3afb76] {
  max-height: 42vh;
  overflow-y: scroll;
}
.submitStyle[data-v-bc3afb76] {
  left: 0.625rem;
  right: 0.625rem;
  bottom: calc(constant(safe-area-inset-bottom) + 0.625rem);
  bottom: calc(env(safe-area-inset-bottom) + 0.625rem);
  background-color: #007aff;
  color: #fff;
  height: 2.5rem;
  margin-top: 0.625rem;
  width: 95%;
}
.submit[data-v-bc3afb76] {
  position: fixed;
  left: 0.625rem;
  right: 0.625rem;
  bottom: calc(constant(safe-area-inset-bottom) + 0.625rem);
  bottom: calc(env(safe-area-inset-bottom) + 0.625rem);
  background-color: #007aff;
  color: #fff;
  height: 2.5rem;
}
.submitCropping[data-v-bc3afb76] {
  left: 0.625rem;
  right: 0.625rem;
  bottom: calc(constant(safe-area-inset-bottom) + 0.625rem);
  bottom: calc(env(safe-area-inset-bottom) + 0.625rem);
  background-color: #007aff;
  color: #fff;
  height: 2.5rem;
  width: 95%;
  z-index: 1 !important;
}
.detailContent[data-v-bc3afb76] {
  background-color: #ffffff;
}
.detailContent .title[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.5rem;
  padding: 0 0;
  border-bottom: 0.03125rem solid #e5e7eb;
  font-weight: bold;
}
.detailContent .title .txt[data-v-bc3afb76] {
  margin-left: 0.46875rem;
}
.detailContent .title .status[data-v-bc3afb76] {
  font-size: 0.8125rem;
  font-weight: normal;
}
.detailContent .title .status.success[data-v-bc3afb76] {
  color: #007aff;
}
.detailContent .title .status.fail[data-v-bc3afb76] {
  color: #dd524d;
}
.detailContent .cont .jobs[data-v-bc3afb76] {
  font-size: 0.875rem;
}
.detailContent .cont .jobs .row[data-v-bc3afb76] {
  line-height: 2.4375rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detailContent .cont .jobs .row[data-v-bc3afb76]:nth-child(odd) {
  background-color: #f5f9ff;
}
.detailContent .cont .jobs .row .cell[data-v-bc3afb76]:nth-child(1) {
  width: 5.625rem;
}
.detailContent .cont .jobs .row .cell[data-v-bc3afb76]:nth-child(2) {
  flex: 1;
  text-align: left;
}
.detailContent .cont .jobs .row .cell .item[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detailContent .cont .jobs .row .cell .item .child[data-v-bc3afb76] {
  padding: 0.3125rem 0.1875rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.detailContent .cont .jobs .row .cell .item .child[data-v-bc3afb76]:nth-child(1), .detailContent .cont .jobs .row .cell .item .child[data-v-bc3afb76]:nth-child(2) {
  width: 3.125rem;
}
.detailContent .cont .jobs .row .cell .item .child[data-v-bc3afb76]:nth-child(3) {
  flex: 1;
}
.detailContent .cont .jobs .row .cell .item .child .name[data-v-bc3afb76] {
  flex: 1;
}
.detailContent .cont .jobs .row .cell .item .child .name .primary[data-v-bc3afb76] {
  color: #007aff;
}
.org[data-v-bc3afb76] {
  color: red !important;
}

/***********/
.colorLink[data-v-bc3afb76] {
  float: right;
  line-height: 0.9375rem;
  width: 3.4375rem;
  margin: 0.25rem;
  padding: 0.03125rem !important;
  padding-top: 0.625rem !important;
  color: #007aff;
  font-size: 0.8125rem;
}
.croppingLink[data-v-bc3afb76] {
  float: right;
  line-height: 0.9375rem;
  width: 3.4375rem;
  margin: 0.25rem;
  padding: 0.03125rem !important;
  padding-top: 0.625rem !important;
  color: #007aff;
  font-size: 0.8125rem;
}
.checklist-group[data-v-bc3afb76] {
  padding: 10px 20px !important;
}
.btnModel[data-v-bc3afb76] {
  height: 1.5625rem !important;
  width: 2.1875rem !important;
  margin-left: 0.15625rem !important;
  font-size: 0.6875rem;
}
.detail1[data-v-bc3afb76] {
  max-height: 60vh;
  padding: 0 0.625rem 0.625rem 0.625rem;
  background-color: #ffffff;
}
.detail1 .item[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.625rem;
  min-height: 4rem;
  padding: 0 0.625rem;
}
.detail1 .item .input[data-v-bc3afb76] {
  border: 1px gray solid;
  height: 2.375rem;
  width: 100%;
  border-radius: 0.3125rem;
}
.detail1 .item .submit[data-v-bc3afb76] {
  width: 100%;
  height: 2.375rem;
  color: white;
  background-color: #007aff;
}
.detail1 .item .submit1[data-v-bc3afb76] {
  width: 100%;
  height: 2.375rem;
  color: white;
  background-color: #007aff;
}
.detail1 .title[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.5rem;
  padding: 0 0;
  border-bottom: 0.03125rem solid #e5e7eb;
  font-weight: bold;
}
.detail1 .title .status[data-v-bc3afb76] {
  font-size: 0.8125rem;
  font-weight: normal;
}
.detail1 .title .status.success[data-v-bc3afb76] {
  color: #007aff;
}
.detail1 .title .status.fail[data-v-bc3afb76] {
  color: #dd524d;
}
.detail1 .cont .jobs[data-v-bc3afb76] {
  font-size: 0.875rem;
}
.detail1 .cont .jobs .scrollView[data-v-bc3afb76] {
  max-height: 79vh;
  padding: 0.625rem 0.625rem 0.625rem 0.625rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail1 .cont .jobs .row[data-v-bc3afb76] {
  padding: 0.625rem 0.625rem 0.625rem 0.625rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail1 .cont .jobs .row[data-v-bc3afb76]:nth-child(odd) {
  background-color: #f5f9ff;
}
.detail1 .cont .jobs .row .cell[data-v-bc3afb76]:nth-child(1) {
  width: 18.75rem;
}
.detail1 .cont .jobs .row .cell[data-v-bc3afb76]:nth-child(2) {
  flex: 1;
}
.detail1 .cont .jobs .row .cell .item[data-v-bc3afb76] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.detail1 .cont .jobs .row .cell .item .child[data-v-bc3afb76] {
  padding: 0.3125rem 0.1875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18.75rem;
}
.detail1 .cont .jobs .row .cell .item .child .name[data-v-bc3afb76] {
  flex: 1;
}
.detail1 .cont .jobs .row .cell .item .child .name .primary[data-v-bc3afb76] {
  color: #007aff;
}
.submit1[data-v-bc3afb76] {
  left: 0.625rem;
  right: 0.625rem;
  background-color: #007aff;
  color: #fff;
  height: 2.5rem;
  width: 7.8125rem;
}
.submit2[data-v-bc3afb76] {
  left: 0.625rem;
  right: 0.625rem;
  height: 2.5rem;
  width: 7.8125rem;
}