"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var nt=Object.create;var Le=Object.defineProperty;var at=Object.getOwnPropertyDescriptor;var it=Object.getOwnPropertyNames;var lt=Object.getPrototypeOf,st=Object.prototype.hasOwnProperty;var xe=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var rt=(e,r,u,p)=>{if(r&&typeof r=="object"||typeof r=="function")for(let C of it(r))!st.call(e,C)&&C!==u&&Le(e,C,{get:()=>r[C],enumerable:!(p=at(r,C))||p.enumerable});return e};var se=(e,r,u)=>(u=e!=null?nt(lt(e)):{},rt(r||!e||!e.__esModule?Le(u,"default",{value:e,enumerable:!0}):u,e));var _e=(e,r,u)=>new Promise((p,C)=>{var l=v=>{try{b(u.next(v))}catch(P){C(P)}},m=v=>{try{b(u.throw(v))}catch(P){C(P)}},b=v=>v.done?p(v.value):Promise.resolve(v.value).then(l,m);b((u=u.apply(e,r)).next())});var pe=xe((Lt,Ge)=>{Ge.exports=Vue});var ge=xe((xt,Be)=>{Be.exports=uni.Pinia});var t=se(pe()),De=se(ge()),ut="onShow",dt="onHide",ct="onLoad",pt="onReady",gt="onUnload",vt="onNavigationBarButtonTap";function w(e,r,...u){uni.__log__?uni.__log__(e,r,...u):console[e].apply(console,[...u,r])}function ve(e,r){return typeof e=="string"?r:e}var ne=(e,r=0)=>(u,p=(0,t.getCurrentInstance)())=>{!t.isInSSRComponentSetup&&(0,t.injectHook)(e,u,p)},he=ne(ut,3),Ie=ne(dt,3),Ue=ne(ct,2),ht=ne(pt,2),re=ne(gt,2),Re=ne(vt,2),ft={"ml-player":{"":{position:"relative",zIndex:0}},rateIcon:{".ml-player ":{position:"absolute",right:3,top:95,zIndex:1}},rateText:{".ml-player .rateIcon ":{fontSize:11,color:"#ffffff",backgroundColor:"rgba(0,0,0,0.5)",paddingTop:"5rpx",paddingRight:"8rpx",paddingBottom:"5rpx",paddingLeft:"8rpx",borderRadius:"8rpx",height:22,lineHeight:22}},"center-play-mask":{".ml-player ":{position:"fixed",top:0,left:0,alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)"}},"center-play-btn":{".ml-player .center-play-mask ":{width:"140rpx",height:"140rpx"}},"center-loading":{".ml-player .center-play-mask ":{width:"100rpx",height:"100rpx"}}},ue=(e,r)=>{let u=e.__vccOpts||e;for(let[p,C]of r)u[p]=C;return u},mt=Object.assign({name:"MlPlayer"},{__name:"ml-player",props:{showPlayer:{type:Boolean,default:!0},video:{type:Object,default:{},required:!0},danmu:{danmuList:{type:Array,default:[]},danmuBtn:{type:Boolean,default:!1},enableDanmu:{type:Boolean,default:!1}},videoOptions:{type:Object,default:{width:0,height:0,fillHeight:!1,controls:!0,autoplay:!0,loop:!0,muted:!1,initialTime:0,duration:0,showPoster:!0,showProgress:!0,showCenterPlayBtn:!0,enablePlayGesture:!1,showLoading:!0,enableProgressGesture:!0,objectFit:"contain",playBtnPosition:"center",mobilenetHintType:1,autoPauseIfNavigate:!0,autoPauseIfOpenNative:!0,vslideGesture:!0,vslideGestureInFullscreen:!0,codec:"hardware",httpCache:!0,playStrategy:2,header:{},isLive:!1,showRate:!0,showFit:!0,rateList:["0.5","0.8","1.0","1.25","1.5","2.0"],enableClick:!1,enableDblClick:!1,showWaitingTips:!0,waitingCount:5,waitingMessage:"\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73"}}},emits:["videoClick","doubleClick","play","pause","ended","timeupdate","error","fullscreenchange","waiting","progress","loadedmetadata","fullscreenclick","controlstoggle","playVideo"],setup(e,{emit:r}){var Pe,Te,Oe,ke;var u,p,C,l,m,b,v,P,T,S,O,A,M,D,U,z,$,j,F,V,J,Q,R,I,q,H,d,L,E,ee,X,f,c,n,te;let s=e,k=(0,t.ref)(!1),N=uni.getSystemInfoSync(),oe=(0,t.ref)(((u=s.videoOptions)==null?void 0:u.width)||N.windowWidth),Y=(0,t.ref)(((p=s.videoOptions)==null?void 0:p.height)||Math.ceil(N.windowHeight*.33)),me=(0,t.ref)(Y.value);((C=s.videoOptions)==null?void 0:C.fillHeight)==!0&&(Y.value=N.windowHeight,me.value=Y.value);let g=(0,t.ref)({width:oe.value,height:Y.value,controls:((l=s.videoOptions)==null?void 0:l.controls)!=!1,autoplay:((m=s.videoOptions)==null?void 0:m.autoplay)!=!1,loop:((b=s.videoOptions)==null?void 0:b.loop)!=!1,muted:((v=s.videoOptions)==null?void 0:v.muted)==!0,initialTime:(Pe=(P=s.videoOptions)==null?void 0:P.initialTime)!=null?Pe:0,duration:(Te=(T=s.videoOptions)==null?void 0:T.duration)!=null?Te:0,showPoster:((S=s.videoOptions)==null?void 0:S.showPoster)!=!1,showProgress:((O=s.videoOptions)==null?void 0:O.showProgress)!=!1,showCenterPlayBtn:!1,enablePlayGesture:((A=s.videoOptions)==null?void 0:A.enablePlayGesture)==!0,showLoading:((M=s.videoOptions)==null?void 0:M.showLoading)!=!1,enableProgressGesture:((D=s.videoOptions)==null?void 0:D.enableProgressGesture)!=!1,objectFit:((U=s.videoOptions)==null?void 0:U.objectFit)||"contain",playBtnPosition:((z=s.videoOptions)==null?void 0:z.playBtnPosition)||"center",mobilenetHintType:(Oe=($=s.videoOptions)==null?void 0:$.mobilenetHintType)!=null?Oe:1,autoPauseIfNavigate:((j=s.videoOptions)==null?void 0:j.autoPauseIfNavigate)!=!1,autoPauseIfOpenNative:((F=s.videoOptions)==null?void 0:F.autoPauseIfOpenNative)!=!1,vslideGesture:((V=s.videoOptions)==null?void 0:V.vslideGesture)!=!1,vslideGestureInFullscreen:((J=s.videoOptions)==null?void 0:J.vslideGestureInFullscreen)!=!1,codec:((Q=s.videoOptions)==null?void 0:Q.codec)||"hardware",httpCache:((R=s.videoOptions)==null?void 0:R.httpCache)!=!1,playStrategy:(ke=(I=s.videoOptions)==null?void 0:I.playStrategy)!=null?ke:2,header:((q=s.videoOptions)==null?void 0:q.header)||{},isLive:((H=s.videoOptions)==null?void 0:H.isLive)==!0,showRate:((d=s.videoOptions)==null?void 0:d.showRate)!=!1,rateList:((L=s.videoOptions)==null?void 0:L.rateList)||["0.5","0.8","1.0","1.25","1.5","2.0"],enableClick:((E=s.videoOptions)==null?void 0:E.enableClick)==!0,enableDblClick:((ee=s.videoOptions)==null?void 0:ee.enableDblClick)==!0,showWaitingTips:((X=s.videoOptions)==null?void 0:X.showWaitingTips)!=!1,waitingCount:((f=s.videoOptions)==null?void 0:f.waitingCount)||5,waitingMessage:((c=s.videoOptions)==null?void 0:c.waitingMessage)||"\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73",fillHeight:((n=s.videoOptions)==null?void 0:n.fillHeight)==!0,showFit:((te=s.videoOptions)==null?void 0:te.showFit)!=!1}),ye=(0,t.getCurrentInstance)(),h=null,be=(0,t.ref)(1),ae=(0,t.ref)(0),we=(0,t.ref)(null),Z=(0,t.ref)(!1),Ce=(0,t.computed)(()=>s.showPlayer!=!1),Se=0,ce=0,x=r;ht(()=>{(!h||h==null)&&setTimeout(()=>{K()},500)});let K=()=>{var y;let _=!h||h==null,G=s.video||{};return _&&G&&G.url&&((y=G.url)==null?void 0:y.length)>0&&(h=uni.createVideoContext("ml-player",ye)),h},We=()=>{K(),Z.value=!0,k.value=!1,x("play",h)},Me=()=>{K(),Z.value=!1,k.value=!1,x("pause",h)},ze=()=>{K(),ae.value=0,k.value=!1,x("ended",h)},je=()=>{K(),h&&(h==null||h.play(),Z.value=!0,k.value=!1),x("playVideo",h)},Fe=y=>{Z.value=!0,k.value=!1,x("timeupdate",h,y)};function Ve(y){x("fullscreenchange",h,y)}let Je=()=>{if(k.value=!0,g.value.showWaitingTips){if(ae.value>=g.value.waitingCount)return le(g.value.waitingMessage,"none"),ae.value=0,!1;ae.value=ae.value+1}K(),x("waiting",h)},Qe=()=>{let y=["\u5305\u542B","\u586B\u5145","\u8986\u76D6"];uni.showActionSheet({title:"\u8BBE\u7F6E\u5C55\u793A\u5F62\u5F0F",alertText:"\u9009\u62E9\u5C55\u793A\u5F62\u5F0F",itemList:y,success:function(_){let G=_.tapIndex,ot=["contain","fill","cover"][G];g.value.objectFit=ot,le("\u5C55\u793A\u5F62\u5F0F\uFF1A"+y[G],"none")}})},He=()=>{let y=g.value.rateList||["0.5","0.75","1.0","1.25","1.5","1.75","2.0","2.5"];y&&y.length>0&&uni.showActionSheet({title:"\u8BBE\u7F6E\u500D\u901F",alertText:"\u9009\u62E9\u500D\u901F",itemList:y,success:function(_){let G=Number(y[_.tapIndex]);h||K(),h&&!isNaN(G)&&(be.value=G,h==null||h.playbackRate(G),le("\u500D\u901F\uFF1A"+G,"none"))}})},Ze=y=>{le("\u8D44\u6E90\u64AD\u653E\u9519\u8BEF","error"),Z.value=!1,k.value=!1;let _={video:s.video,error:y};w("error","at components/ml-player/ml-player.vue:387","==========\u8D44\u6E90\u64AD\u653E\u9519\u8BEF========="),w("error","at components/ml-player/ml-player.vue:388",_),x("error",h,y)};function Ke(y){x("progress",h,y)}function Xe(y){x("loadedmetadata",h,y)}function Ye(y){x("fullscreenclick",h,y)}function $e(y){x("controlstoggle",h,y)}let et=()=>{g.value.enableClick==!0&&(K(),x("videoClick",h,s.video))},tt=()=>{let y=Date.now(),_=Se;Se=y,y-_<200?(clearTimeout(ce),g.value.enableDblClick==!0&&x("doubleClick",h,s.video)):ce=setTimeout(()=>{et()},200)},le=(y,_)=>{uni.hideToast(),uni.showToast({title:y,icon:_||"none",mask:!1,duration:2e3})};function Ae(){h&&(h==null||h.pause(),h==null||h.stop()),clearTimeout(ce),h=null,we.value=null,g.value=null,ye=null}return Ie(()=>{h&&(h==null||h.pause())}),re(()=>{Ae()}),(0,t.onUnmounted)(()=>{Ae()}),(y,_)=>((0,t.openBlock)(),(0,t.createElementBlock)(t.Fragment,null,[Ce.value&&e.video&&e.video.url?((0,t.openBlock)(),(0,t.createElementBlock)("u-video",{key:0,class:"ml-player",id:"ml-player",ref_key:"mlPlayer",ref:we,src:e.video.url,poster:e.video.poster,title:e.video.title,controls:g.value.controls,autoplay:g.value.autoplay,loop:g.value.loop,muted:g.value.muted,initialTime:g.value.initialTime,duration:g.value.duration,showFullscreenBtn:g.value.controls,showPlayBtn:g.value.controls,showCenterPlayBtn:g.value.showCenterPlayBtn,showLoading:g.value.showLoading,enableProgressGesture:g.value.enableProgressGesture,objectFit:g.value.objectFit,showMuteBtn:g.value.controls,playBtnPosition:g.value.playBtnPosition,autoPauseIfNavigate:g.value.autoPauseIfNavigate,autoPauseIfOpenNative:g.value.autoPauseIfOpenNative,vslideGesture:g.value.vslideGesture,vslideGestureInFullscreen:g.value.vslideGestureInFullscreen,codec:g.value.codec,httpCache:g.value.httpCache,playStrategy:g.value.playStrategy,showProgress:g.value.showProgress,pageGesture:g.value.vslideGesture,mobilenetHintType:g.value.mobilenetHintType,enablePlayGesture:g.value.enablePlayGesture,isLive:g.value.isLive,onClick:(0,t.withModifiers)(tt,["stop"]),onPlay:We,onPause:Me,onEnded:ze,onTimeupdate:Fe,onFullscreenchange:Ve,onWaiting:Je,onError:Ze,onProgress:Ke,onLoadedmetadata:Xe,onFullscreenclick:Ye,onControlstoggle:$e,webkitPlaysinline:"true",playsinline:"true",xWebkitAirplay:"allow",x5VideoPlayerType:"h5-page",x5VideoOrientation:"portrait",style:(0,t.normalizeStyle)({width:oe.value+"px",height:Y.value+"px"})},[(0,t.createElementVNode)("u-scalable",{style:{position:"absolute",left:"0",right:"0",top:"0",bottom:"0"}},[g.value.showRate||g.value.showFit?((0,t.openBlock)(),(0,t.createElementBlock)("cover-view",{key:0,class:"rateIcon",style:(0,t.normalizeStyle)({top:Y.value/2+"px"})},[g.value.showRate?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:0,onClick:(0,t.withModifiers)(He,["stop"])},[(0,t.createElementVNode)("u-text",{class:"rateText"},(0,t.toDisplayString)(be.value)+"\xD7",1)])):(0,t.createCommentVNode)("",!0),g.value.showFit?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,style:{"margin-top":"2px"},onClick:(0,t.withModifiers)(Qe,["stop"])},[(0,t.createElementVNode)("u-text",{class:"rateText",style:{"font-size":"17px","line-height":"17px"}},"\u2699")])):(0,t.createCommentVNode)("",!0)],4)):(0,t.createCommentVNode)("",!0),(0,t.createElementVNode)("cover-view",null,[(0,t.renderSlot)(y.$slots,"default")]),!Z.value||k.value?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,class:"center-play-mask",style:(0,t.normalizeStyle)({width:oe.value+"px",height:me.value+"px"})},[!k.value&&!Z.value?((0,t.openBlock)(),(0,t.createElementBlock)("cover-image",{key:0,src:"data:image/png;base64,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",class:"center-play-btn",onClick:(0,t.withModifiers)(je,["stop"])})):k.value?((0,t.openBlock)(),(0,t.createElementBlock)("cover-image",{key:1,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABX9JREFUaEPtWVuME2UU/s5MFwFFCRh4gMJ2irLMbNspm0B0o/IgoIaoGORBMQZFY6IRlPhiRMVLiAleow9GhA1GgkYMvmi8BWIIKAE63e502Ww7Xa4KSiSCLnbbOTKlXdo67XR70Uqcx3/O+c73/ef/z38j1Pj5+8NTRdG1mBmnNK+ypRhuXiIx+rT5+zoQ3QtgCMAmTVKeqTHssDvVAjQ71tthCua3AK7K4miDSfHGvra2Mzlc1dA/ALAsPw4Db4UlZWUtsXO+NQlQDX3gfI9OLyTCGzSp/SGrzRfv9YlkdtsR5RbX1LB75rFaRVQtoKM/4k2LQsyGgKFJitdqV+P6PSB8aEeSmOaHvPI31r+ZBw+OG9uSWsSC4AbSezWPb2elwqoXcHzf2PS50b8ANKYwGH2lSfJCq609FnG7BOGwDZmzoKRb8wRPB/p1hQT+EkRTcnYMbAtLypJKRDgKyPbiUgZiLoFe3d8q/5gDDhrR+xi8OS/QbybEOd1SW9/wHEhEV4H59QIyhOWaR+my2gKG3ktAWzFZBq0JS/JLTiLKCggY+lICPsqbMJGQpPjzQYMD+q1gWmFVIVMQ1nW3tiWKg6oJ/Q4wloGQBFOXJslfWzZ+Q79BAL4rQXJ4KJYTUVaAakTDABcQBmhBjoBT7zj9D8ajNzNxRozNd1yTlOFhVQrLQYB+AEAw3zl/8jkRdPo/t7//yj/FpFWJrvibLaNL8yrLnTCcBNwN4ONyQ8gpgNP/wEB0MZn8aZHdobSYDkam+3918necxAFDX0bAErtJ7ARe6f/sgrgWwGxi2vzHkPCytRh29PVdnXalFkKgKTDRo3nlz4sxHQVUSqLedrMT+i0mY2veKg8CIkMw5/dIvhN5o6LeoWvHU47oE1qG0A9ggg3aJ5qkWEM78w1n4EJJo7fB5kSQ8LwmyRtqp1IdQtDQVzOwvpR3yjSn9czwHSkQULyvyTeqjkb1XqoRfRvgR0uXTropJMmZ9SOTATXW0wmBdhU4ED2heeQ3qqdRvWe2cFi7WNsvOYhxUUU5ezEDzIKaiFobM0/WwySX6A5NaztePY3qPeft2OE63TpJA0MpRmHGC2Gv8lzpOQCeDPAaTWp/r3oKtXv6DnVLYlq09lmdF9HoHU2SH8tHb9oymiPpT+hzBZM6qEXYbjciml5ATohqROeDuZNAJ1KgXRHvrEhBFao96Y1DUI1oF8D3F0V4SpOU9U2fgUBcX0uEZ227h3BnRoA/Fp4ExqjuawJHG9ePI0duNyKTXRB+KunJfIwKdoN5J6WRh6u/R4mdauFypRq6dYJqzbaeTA7CHVWUZP3pjBwxU4EY35fxNElN6DEwMrcIAH4eT2On7fR4zo08XGM81LiugyDboTPzVgrEI3cRCdssA2Y8EPYqmxpDpTrUzK2FiB8AXF6EcDgtptWmr0IW6ayIjQDmZEQwtqfIfMQ6F/wnBJTL3f8CqhvZ9fO6tDJgXSMS6GTu0rV+/dQ4pOEMBAz9TQIet0IRaEVIkt9vXNj6IecJ6NlHoI6MAMLGkEd5sH5hGoeUL+BhAr2bCWWandoM3+7Gha0fcsEktg73gsBnDkg+21eV+oWtHGnO0d6JQylcG2qdtcfO69KqQpX3S/NYXvoZsE5FLRAXjW4Zt2WP2z3YPH1/gUnZDMi6PuqyMYgx4D7/ZldwqdosQhyHkGro1iPDeDC+0LzKbY0iblWbvVNnnRopvqOAgBFZQCxcLwrprfs9/oMjDVCpvRrveQUC9eZeLyv1cxRQKdC/ZVezgOAhXTbTeJLN1NPdMwIn/2khNQtQDf0zALcz6MWwJNteQAWO9E2hodRrZjq1ut53T7ULyDxi0yoB6ZWltiDBgd7r2DR3N2KP9RdxLfhyI6cVtQAAAABJRU5ErkJggg==",class:"center-loading"})):(0,t.createCommentVNode)("",!0),!Z.value||k.value?((0,t.openBlock)(),(0,t.createElementBlock)("u-text",{key:2,style:{color:"#fff","text-align":"center"}},"\u70B9\u51FB\u64AD\u653E")):(0,t.createCommentVNode)("",!0)],4)):(0,t.createCommentVNode)("",!0)])],44,["src","poster","title","controls","autoplay","loop","muted","initialTime","duration","showFullscreenBtn","showPlayBtn","showCenterPlayBtn","showLoading","enableProgressGesture","objectFit","showMuteBtn","playBtnPosition","autoPauseIfNavigate","autoPauseIfOpenNative","vslideGesture","vslideGestureInFullscreen","codec","httpCache","playStrategy","showProgress","pageGesture","mobilenetHintType","enablePlayGesture","isLive"])):(0,t.createCommentVNode)("",!0),!Ce.value&&g.value.showPoster&&e.video.poster?((0,t.openBlock)(),(0,t.createElementBlock)("u-image",{key:1,src:e.video.poster,style:(0,t.normalizeStyle)({width:oe.value+"px",height:Y.value+"px"})},null,12,["src"])):(0,t.createCommentVNode)("",!0)],64))}}),yt=ue(mt,[["styles",[ft]]]),bt={"ml-swiper-item-view-box":{"":{position:"relative",backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item-view-right-box":{"":{position:"absolute",bottom:100,paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5,right:1,flexWrap:"wrap",flexDirection:"column"}},"ml-swiper-item-view-bottom-box":{"":{position:"absolute",bottom:1,left:0,flexWrap:"wrap",flexDirection:"column"}},"center-play-mask":{"":{position:"fixed",top:0,left:0,alignItems:"center",justifyContent:"center"}},"center-play-btn":{".center-play-mask ":{width:"140rpx",height:"140rpx"}},"center-loading":{".center-play-mask ":{width:"100rpx",height:"100rpx"}},"ml-swiper-view":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item-video":{"":{backgroundColor:"rgba(0,0,0,0.7)"}}},wt=Object.assign({name:"ml-swiper"},{__name:"ml-swiper",props:{width:{type:Number,default:0,required:!1},height:{type:Number,default:0,required:!1},rightStyle:{type:[Object,String],default:{},required:!1},bottomStyle:{type:[Object,String],default:{},required:!1},videoList:{type:Array,default:[],required:!0},count:{type:Number,default:2,required:!1},showPlay:{type:Boolean,default:!0,required:!1},hidePause:{type:Boolean,default:!0,required:!1}},emits:["change","play","pause","ended","error","waiting","videoClick","doubleClick","loadMore","maskClick"],setup(e,{emit:r}){let u=uni.getSystemInfoSync(),p=e,C=(0,t.ref)({width:p.width||u.windowWidth,height:p.height||u.windowHeight,controls:!1,initialTime:0,vslideGesture:!1,showRate:!1,showFit:!1,enableClick:!0,enableDblClick:!0,autoplay:!0,showPoster:!0,loop:!0,muted:!1,objectFit:"contain"}),l=null,m=(0,t.ref)(0),b=(0,t.computed)(()=>p.videoList||[]),v=(0,t.ref)(!1),P=(0,t.ref)(!1),T=(0,t.ref)(!0),S=(0,t.computed)(()=>({width:C.value.width+"px",height:C.value.height+"px"})),O=r,A=d=>{if(v.value)return;v.value=!0,setTimeout(()=>{v.value=!1},500);let L=d.detail.current;I(),m.value=L,M()},M=()=>{D();let d=b.value.length;O("change",m.value,d)},D=()=>{let d=b.value.length,L=Number(isNaN(p.count)?2:p.count);Number(Number(d)-L)==m.value&&O("loadMore",m.value,d)},U=0,z=d=>{if(Date.now()-U<200){R(l,b.value[m.value]),U=0;return}U=Date.now()},$=d=>{d&&(l=d),P.value=!0,T.value=!1,O("play",l)},j=d=>{d&&(l=d),P.value=!1,O("pause",l)},F=d=>{d&&(l=d),T.value=!0,O("waiting",l)},V=d=>{d&&(l=d),O("ended",l)},J=(d,L)=>{d&&(l=d),T.value=!1,O("error",l,L)},Q=(d,L)=>{d&&(l=d),P.value===!0?I():q(),O("videoClick",l,L)},R=(d,L)=>{d&&(l=d),O("doubleClick",l,L)};function I(){if(l)try{l==null||l.pause(),P.value=!1}catch(d){w("error","at components/ml-swiper/ml-swiper.vue:366",d)}}function q(){if(l){w("log","at components/ml-swiper/ml-swiper.vue:373",l);try{l==null||l.play(),P.value=!0,T.value=!1}catch(d){w("error","at components/ml-swiper/ml-swiper.vue:379",d)}}}function H(){if(l)try{l==null||l.stop()}catch(d){w("error","at components/ml-swiper/ml-swiper.vue:389",d)}}return he(()=>{p.showPlay&&q()}),(0,t.onMounted)(()=>{}),Ie(()=>{p.hidePause&&I()}),re(()=>{U=0,H(),m.value=0,v.value=!1,P.value=!1,T.value=!0,l=null}),(d,L)=>{let E=ve((0,t.resolveDynamicComponent)("ml-player"),yt),ee=(0,t.resolveComponent)("swiper-item"),X=(0,t.resolveComponent)("swiper");return(0,t.openBlock)(),(0,t.createElementBlock)("view",{class:"ml-swiper-view",renderWhole:!0},[b.value&&b.value.length>0?((0,t.openBlock)(),(0,t.createBlock)(X,{key:0,class:"ml-swiper",current:m.value,circular:!1,vertical:!0,"skip-hidden-item-layout":!0,style:(0,t.normalizeStyle)(S.value),onTouchend:z,onChange:A},{default:(0,t.withCtx)(()=>[((0,t.openBlock)(!0),(0,t.createElementBlock)(t.Fragment,null,(0,t.renderList)(b.value,(f,c)=>((0,t.openBlock)(),(0,t.createBlock)(ee,{class:"ml-swiper-item",key:c},{default:(0,t.withCtx)(()=>[(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-box",style:(0,t.normalizeStyle)(S.value)},[(0,t.createVNode)(E,{showPlayer:m.value===c,video:f,videoOptions:C.value,onVideoClick:Q,onDoubleClick:R,onPlay:$,onPause:j,onWaiting:F,onEnded:V,onError:J},null,8,["showPlayer","video","videoOptions"]),(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-right-box",style:(0,t.normalizeStyle)(e.rightStyle)},[(0,t.renderSlot)(d.$slots,"right",{video:f,index:c})],4),(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-bottom-box",style:(0,t.normalizeStyle)(e.bottomStyle)},[(0,t.renderSlot)(d.$slots,"bottom",{video:f,index:c})],4)],4)]),_:2},1024))),128))]),_:3},8,["current","style"])):(0,t.createCommentVNode)("",!0)])}}}),qe=ue(wt,[["styles",[bt]]]),Ct=(e,r,u)=>new Promise((p,C)=>{let l=!1,{method:m="POST",header:b,isLoad:v=!0}=u;v&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),l=!0);let P=e;uni.request({url:P,method:m,data:r,header:b,success:T=>{let{data:S}=T;switch(l&&uni.hideLoading(),S.code){case 0:p(S);break;default:S.msg&&!S.msg.includes("\u67E5\u8BE2\u6570\u636E\u4E0D\u5B58\u5728")&&uni.showToast({title:S.msg||"\u7F51\u7EDC\u51FA\u9519",icon:"none"}),S.data.reload&&(uni.clearStorageSync(),uni.reLaunch({url:"/pagesSub/pages/login/login"})),C(S.msg||"\u7F51\u7EDC\u51FA\u9519");break}},complete:T=>{l&&uni.hideLoading(),w("log","at apis/https.js:53",P,r,T)}})}),St=(e,r,u)=>new Promise((p,C)=>{let{method:l="POST",header:m,isLoad:b=!0}=u,v=!1;b&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),v=!0);let P=e;uni.request({url:P,method:l,data:r,header:m,success:T=>{let{data:S}=T;switch(v&&uni.hideLoading(),S.ErrCode){case"0":p(S);break;default:S.ErrCode!=="60001"&&uni.showToast({title:S.ErrMsg||"\u7F51\u7EDC\u51FA\u9519",icon:"none"}),p(S);break}},complete:T=>{v&&uni.hideLoading(),w("log","at apis/httpsNew.js:53",P,r,T)}})}),ie=(0,De.defineStore)("userInfo",()=>{let e=(0,t.ref)(""),r=(0,t.ref)({}),u=(0,t.ref)(0),p=(0,t.ref)(""),C=(0,t.ref)(0),l=(0,t.ref)(""),m=(0,t.ref)(""),b=(0,t.ref)(0),v=(0,t.ref)(0);function P(O){let A=O.user.roles[0];r.value=O.user,e.value=O.token,A!=null&&(u.value=A.teamID,p.value=A.teamName,C.value=A.companyID,l.value=A.companyName,m.value=A.roleName,b.value=A.role_ID,v.value=A.beanRate)}function T(O,A){switch(O){case"token":e.value=A;break;case"user":r.value=A;break;case"teamID":u.value=A;break;case"teamName":p.value=A;break;case"companyID":C.value=A;break;case"companyName":l.value=A;break;case"roleName":m.value=A;break;case"roleID":b.value=A;break;case"beanRate":v.value=A;break}}function S(){e.value="",r.value={},u.value=0,p.value="",C.value=0,l.value="",m.value="",b.value=0,v.value=0}return{token:e,user:r,teamID:u,teamName:p,companyID:C,companyName:l,roleName:m,roleID:b,beanRate:v,setInfo:P,setItem:T,clear:S}},{unistorage:!0}),At=(e,r,u)=>new Promise((p,C)=>{let{method:l="POST",header:m,isLoad:b=!0}=u,v=!1;b&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),v=!0);let P=e;uni.request({url:P,method:l,data:r,header:m,success:T=>{let{data:S}=T;switch(v&&uni.hideLoading(),S.ErrCode){case"0":p(S);break;default:p(S);break}},complete:T=>{v&&uni.hideLoading(),w("log","at apis/httpsNewWithoutNotion.js:50",P,r,T)}})}),W={AppUser:{Login:e=>a("appUser/login",e),Register:e=>a("appUser/register",e),CheckExist:e=>a("appUser/checkExist",e,"GET"),UserList:e=>a("appUser/getUserList",e,"GET"),CheckUserStatus:e=>i("Clothing/User/CheckUserStatus",e,"POST"),AddLoginLog:e=>i("Clothing/Staff/AddLoginLog",e,"POST"),ModifyPwd:e=>i("Clothing/Staff/ModifyPwd",e,"POST"),AddMember:e=>i("Clothing/User/AddMember",e,"POST"),CheckUserAuth:e=>i("Clothing/User/CheckUserAuth",e,"POST"),CheckCheckMemberStatus:e=>i("Clothing/User/CheckCheckMemberStatus",e,"POST")},AppRole:{List:()=>a("appRole/getAppRoleList",null,"GET")},Company:{List:()=>a("company/getCompanyList",null,"GET"),Join:e=>a("company/joinCompany",e),Check:e=>a("companyApply/optApply",e,"PUT"),Apply:e=>a("companyApply/findCompanyApply",e,"GET"),DelUser:e=>a("company/deleteStaff",e,"DELETE")},Team:{List:e=>a("team/getTeamList",e,"GET"),Join:e=>a("team/joinTeam",e),ApplyDetail:e=>a("teamApply/findTeamApply",e,"GET"),Check:e=>a("teamApply/optApply",e,"PUT"),DelUser:e=>a("team/deleteMember",e,"DELETE")},Staff:{GroupList:e=>i("Clothing/Staff/GroupList",e,"POST"),UserList:e=>i("Clothing/Staff/UserList",e,"POST"),UserListEnhanced:e=>i("Clothing/Staff/getUserListEnhanced",e,"POST"),GetCompanyList:()=>i("Clothing/Staff/GetCompanyList",null,"POST"),RoleList:()=>i("Clothing/Staff/RoleList",null,"POST"),AddGroup:e=>i("Clothing/Staff/AddGroup",e,"POST"),AddTeam:e=>i("Clothing/Staff/AddTeam",e,"POST"),EditTeam:e=>i("Clothing/Staff/EditTeam",e,"POST"),DeleteTeam:e=>i("Clothing/Staff/DeleteTeam",e,"POST"),UpdateGroup:e=>i("Clothing/Staff/UpdateGroup",e,"POST"),DeleteGroup:e=>i("Clothing/Staff/DeleteGroup",e,"POST"),ExistCompany:e=>i("Clothing/Staff/ExistCompany",e,"POST"),AddCompany:e=>i("Clothing/Staff/AddCompany",e,"POST"),UserRoleList:e=>i("Clothing/Staff/UserRoleList",e,"POST"),UserListByComp:e=>B("Clothing/Staff/UserListByComp",e,"POST")},CroppingRecord:{List:e=>a("croppingRecord/getCroppingRecordList",e,"GET"),Detail:e=>a("croppingRecord/findCroppingRecord",e,"GET"),Update:e=>a("croppingRecord/updateCroppingRecord",e,"PUT"),Add:e=>a("croppingRecord/createCroppingRecord",e),Delete:e=>a("croppingRecord/deleteCroppingRecord",e,"DELETE"),AddNew:e=>a("Clothing/CroppingRecord/createCroppingRecord",e,"POST"),UpdatePrintStatus:e=>i("Clothing/CroppingRecord/UpdatePrintStatus",e,"POST"),GetPrintStatus:e=>i("Clothing/CroppingRecord/GetPrintStatus",e,"POST")},Style:{List:e=>a("style/getStyleList",e,"GET"),Add:e=>a("style/createStyle",e),Detail:e=>a("style/findStyle",e,"GET"),Update:e=>a("style/updateStyle",e,"PUT"),DeleteStyle:e=>i("Clothing/Process/DeleteStyle",e,"POST"),StyleList:e=>i("Clothing/CroppingRecord/StyleList",e,"POST")},Cloth:{List:e=>a("cloth/getClothList",e,"GET"),Add:e=>a("cloth/creatCloth",e),Detail:e=>a("cloth/findCloth",e,"GET"),Update:e=>a("cloth/updateCloth",e,"PUT"),AddCloth:e=>i("Clothing/Cloth/AddCloth",e,"POST"),UpdateCloth:e=>i("Clothing/Cloth/UpdateCloth",e,"POST"),ClothListByPage:e=>i("Clothing/Cloth/ClothListByPage",e,"POST")},Wallet:{List:e=>a("userWallet/getUserWalletList",e,"GET"),My:()=>a("userWallet/getMyWalletList",null,"GET"),QueryWallet:e=>i("Clothing/UserWallet/QueryWallet",e,"POST"),ExportWallet:e=>i("Clothing/UserWallet/Export2",e,"POST"),WageSettle:e=>i("Clothing/UserWallet/WageSettle",e,"POST"),WageSettleCancel:e=>i("Clothing/UserWallet/WageSettleCancel",e,"POST")},Job:{List:e=>a("jobQuestion/getJobQuestionList",e,"GET"),Detail:e=>a("job/findJob",e,"GET"),Question:e=>a("jobQuestion/findJobQuestion",e,"GET"),Update:e=>a("jobQuestion/handleJobQuestion",e,"PUT"),Add:e=>a("jobQuestion/createJobQuestion",e),Job:e=>a("job/getJobList",e,"GET"),Process:e=>a("job/getJobGroupByProcess",e,"GET"),ToApply:e=>a("job/jobAuditApply",e,"PUT"),Check:e=>a("job/jobAuditOpt",e,"PUT"),ChangeWorker:e=>a("job/changeWorker",e,"PUT"),Task:e=>a("job/postJobList",e),Wages:e=>a("job/getWagesDetail",e,"GET"),Apply:{Detail:e=>a("jobApply/findJobApply",e,"GET"),Add:e=>a("jobApply/createJobApply",e),Check:e=>a("jobApply/optApply",e,"PUT")},JobReceiveList:e=>i("Clothing/Job/JobReceiveList",e,"POST"),JobReceiveDetail:e=>i("Clothing/Job/JobReceiveDetail",e,"POST"),UpdateJobStatus:e=>i("Clothing/Job/UpdateJobStatus",e,"POST"),DeleteJob:e=>B("Clothing/Job/DeleteJob",e,"POST"),AddJob:e=>i("Clothing/Job/AddJob",e,"POST"),AddJobForOther:e=>i("Clothing/Job/AddJobForOther",e,"POST"),AddJobAll:e=>i("Clothing/Job/AddJobAll",e,"POST"),JobFinishList:e=>B("Clothing/Job/JobFinishList",e,"POST"),JobReceiveListByCroppingRecord:e=>B("Clothing/Job/JobReceiveListByCroppingRecord",e,"POST"),UpdateJobPrice:e=>i("Clothing/Job/UpdateJobPrice",e,"POST")},Inventory:{Stock:e=>a("inventory/getInventoryList",e,"GET")},Process:{List:e=>a("process/getProcessList",e,"GET"),ListNew:e=>i("Clothing/Process/ProcessListNew",e,"POST"),Detail:e=>a("process/findProcess",e,"GET"),Add:e=>a("process/createProcess",e),Update:e=>a("process/updateProcess",e,"PUT"),UpdateProcess:e=>i("Clothing/Process/UpdateProcess",e,"POST"),AddProcess:e=>i("Clothing/Process/AddProcess",e,"POST"),ProcessList:e=>i("Clothing/Process/ProcessList",e,"POST"),ProcessListByPage:e=>i("Clothing/Process/ProcessListByPage",e,"POST"),GetSizeQuanlity:e=>i("Clothing/Process/GetSizeQuanlity",e,"POST"),GetList:e=>i("Clothing/Process/StyleList",e,"POST"),GetStandard:e=>i("Clothing/Process/GetStandard",e,"POST"),AddStandard:e=>B("Clothing/Process/AddStandard",e,"POST"),GetMyStandard:e=>B("Clothing/Process/GetMyStandard",e,"POST"),StyleSettle:e=>i("Clothing/Process/StyleSettle",e,"POST"),StyleSettleCancel:e=>i("Clothing/Process/StyleSettleCancel",e,"POST")},Chat:{GroupList:e=>i("IM/Group/GroupList",e,"POST"),AddGroup:e=>B("IM/Group/AddGroup",e,"POST"),CreateGroup:e=>i("IM/Group/CreateGroup",e,"POST"),GetUser:e=>i("IM/Chat/GetUser",e,"POST")},Message:{List:e=>a("msgBox/getMyMsgBoxList",e,"GET"),Send:e=>a("msgBox/getMySendMsgList",e,"GET"),Read:e=>a("msgBox/setRead",e,"GET")},Banner:{List:()=>a("banner/getBannerList",null,"GET"),Find:e=>a("banner/findBanner",e,"GET")},Computation:{Do:e=>a("computation/doComputation",e)},Order:{List:e=>a("order/getOrderList",e,"GET"),Detail:e=>a("order/findOrder",e,"GET"),Goods:()=>a("rechargeOption/getRechargeOptionList",null,"GET"),Add:e=>a("order/createOrder",e),Pay:e=>a("order/payOrder",e)},Common:{Request:(e,r,u)=>a(e,r,u),Request1:(e,r,u)=>i(e,r,u),GetInfo:e=>i("Clothing/Common/GetInfo",e,"POST"),AddColors:e=>i("Clothing/Common/AddColors",e,"POST"),GetColors:e=>i("Clothing/Common/GetColors",e,"POST"),Today:e=>i("Clothing/Date/Today",e,"GET"),Now:e=>i("Clothing/Date/Now",e,"GET")},Question:{QuestionList:e=>i("Clothing/Job/QuestionList",e,"POST"),ReplyQuestion:e=>i("Clothing/Job/ReplyQuestion",e,"POST")},Adv:{AdvList:e=>B("ADV/AdvInfo/AdvList",e,"POST"),AddAdv:e=>B("ADV/AdvInfo/AddAdv",e,"POST"),DeleteAdv:e=>i("ADV/AdvInfo/DeleteAdv",e,"POST"),PayAdv:e=>i("ADV/AdvInfo/PayAdv",e,"POST"),BigList:e=>B("ADV/AdvInfo/BigList",e,"POST"),SmallList:e=>B("ADV/AdvInfo/SmallList",e,"POST"),BuyList:e=>i("ADV/AdvInfo/BuyList",e,"POST"),AdvDetial:e=>i("ADV/AdvInfo/AdvDetail",e,"POST")},Url:{baseUrl:"http://8.138.8.6:8889",baseUrlBiz:"http://8.138.8.6:8890",updateUrl:"https://wxapi.ruiruicaikuai.com/",baseUrlFileUpload:"https://wxapi.ruiruicaikuai.com/api/Files/Upload"},App:{vuex_version:"1.3.7",agent_code:""}},Pt="http://8.138.8.6:8889",Ee="http://8.138.8.6:8890",a=(e,r,u="POST")=>{let{token:p}=ie(),C=Pt+"/api/"+e;return Ct(C,r,{header:{"x-token":p},method:u})},i=(e,r,u="POST")=>{ie();let p=Ee+"/api/"+e;return St(p,r,{header:{},method:u})},B=(e,r,u="POST")=>{ie();let p=Ee+"/api/"+e;return At(p,r,{header:{},method:u})};var o=se(pe()),Ne=se(ge());var Tt={tabsviewContent:{"":{position:"fixed","!minWidth":"750rpx"}},tabsview:{"":{"!minWidth":"750rpx"}},imgV:{"":{width:100,textAlign:"center",minHeight:65}},search:{"":{gridTemplateColumns:"repeat(1, minmax(0, 1fr))",paddingTop:"20rpx",paddingRight:"20rpx",paddingBottom:"20rpx",paddingLeft:"20rpx",gap:"20rpx",backgroundColor:"#ffffff"},".top_nav .top_content ":{position:"absolute",right:20,top:"35rpx"}},search_item:{"":{display:"flex",flexDirection:"row",alignItems:"center",gap:"20rpx",fontSize:"28rpx"}},search_item_btn_submit:{"":{backgroundColor:"#007aff",color:"#ffffff"}},submit_deploy:{"":{position:"fixed",left:"20rpx",right:"20rpx",backgroundColor:"#007aff",color:"#ffffff",height:"80rpx",bottom:"10rpx"}},"person-head":{"":{position:"relative",backgroundColor:"#ffffff",marginLeft:"20rpx",marginRight:"20rpx"}},"video-layer":{"":{position:"absolute",right:12,bottom:120,color:"#ffffff"}},"uniui-right":{"":{justifyContent:"center"}},videoTitle:{"":{paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5,color:"#de4a00",fontSize:13,lines:13,whiteSpace:"normal"}},userAvatar:{"":{width:35,height:35,borderRadius:100,marginBottom:10,borderWidth:"1rpx",borderStyle:"solid",borderColor:"#ffffff",backgroundColor:"#fafafa"}},iconTitle:{"":{fontSize:12,color:"#ffffff",textAlign:"center",paddingBottom:5}},top_nav:{"":{position:"fixed",top:0,left:0,right:0,backgroundImage:"linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0))"}},top_content:{".top_nav ":{paddingTop:"30rpx",flexDirection:"row",alignItems:"center",justifyContent:"center"}},player:{".top_nav .top_content ":{position:"absolute",left:20,top:"35rpx"}},img:{".top_nav .top_content ":{width:"44rpx",height:"44rpx"}},content_btn:{".top_nav .top_content ":{flexDirection:"row",width:220,alignItems:"center",justifyContent:"space-around"}},content_item:{".top_nav .top_content .content_btn ":{position:"relative",height:30}},line_on:{".top_nav .top_content .content_btn .content_item ":{position:"absolute",width:30,height:2,backgroundColor:"#FFFFFF",bottom:0,left:2,borderRadius:"4rpx"}},item_title:{".top_nav .top_content .content_btn .content_item ":{color:"#dcdcdc",fontSize:"36rpx",fontWeight:"bold"}},i_on:{".top_nav .top_content .content_btn .content_item ":{fontWeight:"bold",fontSize:"38rpx","!color":"#FFFFFF"}},uContent:{"":{height:"220rpx",lineHeight:"220rpx",borderRadius:"20rpx"}}},Ot={__name:"index",setup(e){let r=ie(),{user:u,roleID:p,teamID:C}=(0,Ne.storeToRefs)(r),l=(0,o.ref)(0),m=(0,o.ref)([]),b=(0,o.ref)([]),v=(0,o.ref)([]),P="";(0,o.ref)([]),(0,o.ref)(""),(0,o.ref)([]);let T=(0,o.ref)(""),S=uni.getSystemInfoSync(),O=S.windowWidth,A=S.windowHeight,M={top:"36px"},D=(0,o.ref)([]),U=(0,o.ref)(0),z=(0,o.ref)(0),$=0,j=(f,c)=>{w("log","at pagesSub/pages/adv/index.nvue:244","onchange-\u5F53\u524D\u7D22\u5F15:",f+"aa"+c),U.value=f,f==0&&uni.showToast({title:"\u5F53\u524D\u5DF2\u662F\u7B2C\u4E00\u4E2A\u89C6\u9891",icon:"none",mask:!1}),f==c-1&&uni.showToast({title:"\u5F53\u524D\u5DF2\u662F\u6700\u540E\u4E00\u4E2A\u89C6\u9891",icon:"none",mask:!1})},F=(f,c)=>{w("log","at pagesSub/pages/adv/index.nvue:256","\u52A0\u8F7D\u66F4\u6240\u89C6\u9891\uFF1A",f+" / "+c),!(z.value>5)&&(L().forEach(n=>{n.title=D.value.length+"\uFF0C"+n.title+n.title+n.title,D.value.push(n)}),z.value=z.value+1)},V=f=>{w("log","at pagesSub/pages/adv/index.nvue:270","\u89C6\u9891\u5F00\u59CB\u64AD\u653E")},J=f=>{w("log","at pagesSub/pages/adv/index.nvue:277","\u89C6\u9891\u6682\u505C\u64AD\u653E")},Q=f=>{w("log","at pagesSub/pages/adv/index.nvue:284","\u89C6\u9891\u64AD\u653E\u7ED3\u675F")},R=(f,c)=>{w("error","at pagesSub/pages/adv/index.nvue:291","\u89C6\u9891\u64AD\u653E\u51FA\u9519\uFF1A",c)},I=f=>{w("error","at pagesSub/pages/adv/index.nvue:298","\u89C6\u9891\u51FA\u73B0\u7F13\u51B2")},q=(f,c)=>{w("log","at pagesSub/pages/adv/index.nvue:305","\u70B9\u51FB\u4E86\u89C6\u9891\uFF1A",c)},H=(f,c)=>{T.value=c.author.tel,w("log","at pagesSub/pages/adv/index.nvue:313","\u53CC\u51FB\u4E86\u89C6\u9891\uFF1A",c),uni.showModal({title:"\u63D0\u793A",content:"\u62E8\u6253\u7535\u8BDD\u7ED9\u53D1\u5E03\u4EBA\uFF1F",confirmText:"\u62E8\u6253",cancelText:"\u53D6\u6D88",success:function(n){n.confirm?uni.makePhoneCall({phoneNumber:T.value}):n.cancel&&w("log","at pagesSub/pages/adv/index.nvue:341","\u7528\u6237\u70B9\u51FB\u53D6\u6D88")}})},d=(f,c)=>{w("log","at pagesSub/pages/adv/index.nvue:350","\u70B9\u51FB\u4E86\u8499\u5C42\uFF1A",f,c)},L=()=>[{videoId:D.value.length+1,title:"\u3002",poster:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",url:"https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",uploadTime:"2023-11-08 19:41",ipLocation:"\u4E0A\u6D77",author:{authorId:101,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u964C\u8DEF",genderName:"\u7537"}}];he(()=>{(0,o.toRaw)(u.value).ID==""&&(r.clear(),uni.reLaunch({url:"/pagesSub/pages/login/login"})),p.value==""&&uni.reLaunch({url:"/pages/my/my"}),L().forEach(c=>{c.title=D.value.length+"\uFF0C"+c.title+c.title+c.title,D.value.push(c)})}),Ue(f=>{w("log","at pagesSub/pages/adv/index.nvue:399","onload"),w("log","at pagesSub/pages/adv/index.nvue:400",f),this.statusBarHeight=uni.getSystemInfoSync().statusBarHeight+"px",this.windowWidth=uni.getSystemInfoSync().screenWidth,X()}),re(()=>{uni.createVideoContext("myVideo",this).pause(),this.mp4Url=""}),Re(()=>{uni.navigateTo({url:"/pagesSub/pages/message/message"})}),(0,o.onMounted)(()=>{w("log","at pagesSub/pages/adv/index.nvue:419","onMounted"),X()});let E=f=>{l.value=f,w("log","at pagesSub/pages/adv/index.nvue:428",f),w("log","at pagesSub/pages/adv/index.nvue:429",l.value),w("log","at pagesSub/pages/adv/index.nvue:430",P)},ee=()=>{uni.removeStorageSync("storage_orderinfo"),l.value==0&&uni.navigateTo({url:"/pagesSub/pages/adv/publishFH"}),l.value==1&&uni.navigateTo({url:"/pagesSub/pages/adv/publishZG"}),l.value==2&&uni.navigateTo({url:"/pagesSub/pages/adv/publishSH"})},X=()=>_e(this,null,function*(){m.value.splice(0),v.value.splice(0),b.value.splice(0);var f=uni.getStorageSync("storage_orderinfo_groupid");w("log","at pagesSub/pages/adv/index.nvue:496",f),W.Adv.SmallList({groupid:f}).then(function(c){w("log","at pagesSub/pages/adv/index.nvue:500",c),c.Success&&c.ResData.length>0&&c.ResData.forEach(function(n,te){if(n.type=="SH"&&v.value.push({videoId:v.value.length+1,title:`\u63A5\u8D27\u6D88\u606F:
	\u5730\u5740:`+n.sh_address+`
	\u59D3\u540D:`+n.sh_name+`
	\u8054\u7CFB\u7535\u8BDD:`+n.sh_tel+`
	\u4EBA\u6570\u89C4\u6A21:`+n.sh_gm+`\u4EBA 
	\u64C5\u957F\u6B3E\u5F0F:`+n.sh_style+`
	\u5305\u6599\u80FD\u529B:`+n.sh_blnl+`
	\u6253\u7248\u5F00\u53D1\u80FD\u529B:`+n.sh_dbkfnl+`
	\u52A0\u5DE5\u96BE\u5EA6:`+n.sh_jgnd+`
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+n.sh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:n.sh_tel}}),n.type=="FH"&&m.value.push({videoId:m.value.length+1,title:`\u53D1\u8D27\u6D88\u606F:
	\u8054\u7CFB\u7535\u8BDD:`+n.fh_Tel+`
	\u671F\u671B\u52A0\u5DE5\u5730\u5740:`+n.fh_address+`
	\u5DE5\u671F\u9650\u5236:`+n.fh_limitGQ+`
	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:`+n.fh_fzType+`
	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:`+n.fh_JBGYBZ+`
	\u8D26\u671F\u671F\u671B:`+n.fh_ZQQW+`
	\u8BA2\u5355\u6570\u91CF:`+n.fh_orderNum+`
	\u662F\u5426\u5305\u88C1:`+n.fh_SFBC+`
	\u662F\u5426\u5305\u9762\u8F85\u6599:`+n.fh_SFMLFZ+`
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+n.fh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:n.fh_Tel}}),n.type=="ZG"){var s=JSON.parse(n.zg_zaopinList),k="";s.forEach(function(N,oe){k+=N.type+"  \u62DB\u8058\u4EBA\u6570:"+N.quantity+`\u4EBA
	`}),b.value.push({videoId:b.value.length+1,title:`\u62DB\u8058\u901A\u77E5:
	\u5730\u5740:`+n.zg_address+`
	\u59D3\u540D:`+n.zg_name+`
	\u8054\u7CFB\u7535\u8BDD:`+n.zg_tel+`
	\u62DB\u8058\u4FE1\u606F:
	`+k,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+n.zg_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:n.zg_tel}})}})}.bind(this)),W.Adv.BigList({groupid:f}).then(function(c){w("log","at pagesSub/pages/adv/index.nvue:587",c),c.Success&&c.ResData.length>0&&c.ResData.forEach(function(n,te){if(n.type=="SH"&&v.value.push({videoId:v.value.length+1,title:`\u63A5\u8D27\u6D88\u606F:
	\u5730\u5740:`+n.sh_address+`
	\u59D3\u540D:`+n.sh_name+`
	\u8054\u7CFB\u7535\u8BDD:`+n.sh_tel+`
	\u4EBA\u6570\u89C4\u6A21:`+n.sh_gm+`\u4EBA 
	\u64C5\u957F\u6B3E\u5F0F:`+n.sh_style+`
	\u5305\u6599\u80FD\u529B:`+n.sh_blnl+`
	\u6253\u7248\u5F00\u53D1\u80FD\u529B:`+n.sh_dbkfnl+`
	\u52A0\u5DE5\u96BE\u5EA6:`+n.sh_jgnd+`
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+n.sh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:n.sh_tel}}),n.type=="FH"&&m.value.push({videoId:m.value.length+1,title:`\u53D1\u8D27\u6D88\u606F:
	\u8054\u7CFB\u7535\u8BDD:`+n.fh_Tel+`
	\u671F\u671B\u52A0\u5DE5\u5730\u5740:`+n.fh_address+`
	\u5DE5\u671F\u9650\u5236:`+n.fh_limitGQ+`
	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:`+n.fh_fzType+`
	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:`+n.fh_JBGYBZ+`
	\u8D26\u671F\u671F\u671B:`+n.fh_ZQQW+`
	\u8BA2\u5355\u6570\u91CF:`+n.fh_orderNum+`
	\u662F\u5426\u5305\u88C1:`+n.fh_SFBC+`
	\u662F\u5426\u5305\u9762\u8F85\u6599:`+n.fh_SFMLFZ+`
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+n.fh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:n.fh_Tel}}),n.type=="ZG"){var s=JSON.parse(n.zg_zaopinList),k="";s.forEach(function(N,oe){k+=N.type+"  \u62DB\u8058\u4EBA\u6570:"+N.quantity+`\u4EBA
	`}),b.value.push({videoId:b.value.length+1,title:`\u62DB\u8058\u901A\u77E5:
	\u5730\u5740:`+n.zg_address+`
	\u59D3\u540D:`+n.zg_name+`
	\u8054\u7CFB\u7535\u8BDD:`+n.zg_tel+`
	\u62DB\u8058\u4FE1\u606F:
	`+k,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+n.zg_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:n.zg_tel}})}}),w("log","at pagesSub/pages/adv/index.nvue:669",m.value)}.bind(this))});return(0,o.reactive)({videoList:[{src:"https://vjs.zencdn.net/v/oceans.mp4",id:"1"},{src:"https://www.w3schools.com/html/movie.mp4",id:"2"},{src:"https://media.w3.org/2010/05/sintel/trailer.mp4",id:"3"},{src:"https://vjs.zencdn.net/v/oceans.mp4",id:"4"},{src:"https://www.w3school.com.cn/example/html5/mov_bbb.mp4",id:"5"}]}),(f,c)=>{let n=ve((0,o.resolveDynamicComponent)("ml-swiper"),qe),te=(0,o.resolveComponent)("button");return(0,o.openBlock)(),(0,o.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,o.createElementVNode)("view",{class:"tabsviewContent"},[(0,o.createElementVNode)("view",{class:"top_nav"},[(0,o.createElementVNode)("view",{style:(0,o.normalizeStyle)({height:(0,o.unref)($)})},null,4),(0,o.createElementVNode)("view",{class:"top_content"},[(0,o.createElementVNode)("view",{class:"content_btn"},[(0,o.createElementVNode)("view",{class:"content_item",onClick:c[0]||(c[0]=s=>E(0))},[(0,o.createElementVNode)("u-text",{class:(0,o.normalizeClass)(["item_title",{i_on:(0,o.unref)(l)===0}])},"\u53D1\u8D27",2),(0,o.unref)(l)==0?((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:0,class:"line_on"})):((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:1}))]),(0,o.createElementVNode)("view",{class:"content_item",onClick:c[1]||(c[1]=s=>E(1))},[(0,o.createElementVNode)("u-text",{class:(0,o.normalizeClass)(["item_title",{i_on:(0,o.unref)(l)===1}])},"\u62DB\u5DE5",2),(0,o.unref)(l)==1?((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:0,class:"line_on"})):((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:1}))]),(0,o.createElementVNode)("view",{class:"content_item",onClick:c[2]||(c[2]=s=>E(2))},[(0,o.createElementVNode)("u-text",{class:(0,o.normalizeClass)(["item_title",{i_on:(0,o.unref)(l)===2}])},"\u63A5\u8D27",2),(0,o.createElementVNode)("view",{view:"",class:(0,o.normalizeClass)({line_on:(0,o.unref)(l)===2})},null,2)])])])])]),(0,o.unref)(l)==0?((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:0,class:"work_1"},[(0,o.createElementVNode)("view",{class:"vedioContent"},[(0,o.createVNode)(n,{videoList:(0,o.unref)(m),width:(0,o.unref)(O),height:(0,o.unref)(A),bottomStyle:M,onLoadMore:F,onChange:j,onPlay:V,onPause:J,onEnded:Q,onError:R,onWaiting:I,onVideoClick:q,onDoubleClick:H,onMaskClick:d},{bottom:(0,o.withCtx)(({video:s,index:k})=>[s?((0,o.openBlock)(),(0,o.createElementBlock)("u-text",{key:0,class:"videoTitle"},(0,o.toDisplayString)(s==null?void 0:s.title),1)):(0,o.createCommentVNode)("",!0)]),_:1},8,["videoList","width","height"])])])):(0,o.createCommentVNode)("",!0),(0,o.unref)(l)==1?((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:1,class:"work_1"},[(0,o.createElementVNode)("view",{class:"vedioContent"},[(0,o.createVNode)(n,{videoList:(0,o.unref)(b),width:(0,o.unref)(O),height:(0,o.unref)(A),bottomStyle:M,onLoadMore:F,onChange:j,onPlay:V,onPause:J,onEnded:Q,onError:R,onWaiting:I,onVideoClick:q,onDoubleClick:H,onMaskClick:d},{bottom:(0,o.withCtx)(({video:s,index:k})=>[s?((0,o.openBlock)(),(0,o.createElementBlock)("u-text",{key:0,class:"videoTitle"},(0,o.toDisplayString)(s==null?void 0:s.title),1)):(0,o.createCommentVNode)("",!0)]),_:1},8,["videoList","width","height"])])])):(0,o.createCommentVNode)("",!0),(0,o.unref)(l)==2?((0,o.openBlock)(),(0,o.createElementBlock)("view",{key:2,class:"work_1"},[(0,o.createElementVNode)("view",{class:"vedioContent"},[(0,o.createVNode)(n,{videoList:(0,o.unref)(v),width:(0,o.unref)(O),height:(0,o.unref)(A),bottomStyle:M,onLoadMore:F,onChange:j,onPlay:V,onPause:J,onEnded:Q,onError:R,onWaiting:I,onVideoClick:q,onDoubleClick:H,onMaskClick:d},{bottom:(0,o.withCtx)(({video:s,index:k})=>[s?((0,o.openBlock)(),(0,o.createElementBlock)("u-text",{key:0,class:"videoTitle"},(0,o.toDisplayString)(s==null?void 0:s.title),1)):(0,o.createCommentVNode)("",!0)]),_:1},8,["videoList","width","height"])])])):(0,o.createCommentVNode)("",!0),(0,o.createVNode)(te,{class:"btn submit_deploy",style:{"z-index":"9998"},onClick:ee},{default:(0,o.withCtx)(()=>[(0,o.createTextVNode)("\u53D1 \u5E03")]),_:1})])}}},de=ue(Ot,[["styles",[Tt]]]);var fe=plus.webview.currentWebview();if(fe){let e=parseInt(fe.id),r="pagesSub/pages/adv/index",u={};try{u=JSON.parse(fe.__query__)}catch(C){}de.mpType="page";let p=Vue.createPageApp(de,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:r,__pageQuery:u});p.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...de.styles||[]])),p.mount("#root")}})();
