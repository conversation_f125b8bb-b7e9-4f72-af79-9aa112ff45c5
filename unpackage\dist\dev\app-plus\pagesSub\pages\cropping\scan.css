/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.issue[data-v-becdbe06] {
  position: fixed;
  top: var(--window-top);
  bottom: var(--window-bottom);
  left: 0;
  right: 0;
  width: 100vw;
  height: calc(100vh - var(--window-top) - var(--window-bottom));
  pointer-events: none;
}
.issue .target[data-v-becdbe06] {
  pointer-events: all;
  pointer-events: x;
  width: 0;
  height: 0;
}
.issue .target .icon[data-v-becdbe06] {
  display: block;
  width: 100%;
  height: 100%;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.empty[data-v-e2280098] {
  display: grid;
  place-items: center;
  min-height: 18.75rem;
  color: #999;
  font-size: 1rem;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.modal[data-v-4bcd74eb] {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 0;
  outline: 0;
  text-align: center;
  transform: scale(1.185);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  perspective: 62.5rem;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}
.modal[data-v-4bcd74eb]::before {
  content: "​";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}
.modal.show[data-v-4bcd74eb] {
  opacity: 1;
  transition-duration: 0.3s;
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}
.modal .dialog[data-v-4bcd74eb] {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 21.25rem;
  max-width: 100%;
  background-color: #f8f8f8;
  border-radius: 0.3125rem;
  overflow: hidden;
  user-select: none;
  -webkit-user-select: none;
}
.modal.bottom[data-v-4bcd74eb] {
  margin-bottom: -31.25rem;
}
.modal.bottom[data-v-4bcd74eb]::before {
  vertical-align: bottom;
}
.modal.bottom.show[data-v-4bcd74eb] {
  margin-bottom: 0;
}
.modal.bottom .dialog[data-v-4bcd74eb] {
  width: 100%;
  border-radius: 0;
}
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.process-tag[data-v-d6dc95a7] {
  font-size: 0.625rem;
  padding: 0.125rem 0.25rem;
  border-radius: 0.125rem;
  margin-left: 0.3125rem;
}
.process-tag.cached[data-v-d6dc95a7] {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 0.03125rem solid #91d5ff;
}
.card-header[data-v-d6dc95a7] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.header-action[data-v-d6dc95a7] {
  font-size: 0.75rem;
  color: #1890ff;
  margin-left: 0.625rem;
}

/* 页面容器 */
.page-container[data-v-d6dc95a7] {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0.625rem;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 3.75rem);
  padding-bottom: calc(env(safe-area-inset-bottom) + 3.75rem);
}

/* 卡片通用样式 */
.info-card[data-v-d6dc95a7], .process-card[data-v-d6dc95a7], .option-card[data-v-d6dc95a7] {
  background-color: #ffffff;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 0.0625rem 0.375rem rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 卡片头部 */
.card-header[data-v-d6dc95a7] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem 0.75rem 1rem;
  border-bottom: 0.03125rem solid #f0f2f5;
}
.header-title[data-v-d6dc95a7] {
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
}
.header-action[data-v-d6dc95a7] {
  font-size: 0.875rem;
  color: #007aff;
  font-weight: 500;
}

/* 基本信息列表 */
.info-list[data-v-d6dc95a7] {
  padding: 0 1rem 1rem 1rem;
}
.info-item[data-v-d6dc95a7] {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.info-item[data-v-d6dc95a7]:last-child {
  border-bottom: none;
}
.info-label[data-v-d6dc95a7] {
  font-size: 0.9375rem;
  color: #666666;
  flex-shrink: 0;
  margin-right: 0.625rem;
}
.info-value[data-v-d6dc95a7] {
  font-size: 0.9375rem;
  color: #1a1a1a;
  font-weight: 500;
  flex: 1;
}
.info-input[data-v-d6dc95a7] {
  font-size: 0.9375rem;
  color: #1a1a1a;
  background-color: #f8f9fa;
  padding: 0.5rem 0.625rem;
  border-radius: 0.25rem;
  border: 0.03125rem solid #e9ecef;
  width: 6.25rem;
  margin-right: 0.5rem;
}
.tips-label[data-v-d6dc95a7] {
  font-size: 0.75rem;
  color: #ff4757;
  flex-shrink: 0;
}

/* 工序列表 */
.process-list[data-v-d6dc95a7] {
  padding: 0 1rem 0.75rem 1rem;
}
.process-item[data-v-d6dc95a7] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.375rem 0;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.process-item[data-v-d6dc95a7]:last-child {
  border-bottom: none;
}
.process-info[data-v-d6dc95a7] {
  flex: 1;
}
.process-name[data-v-d6dc95a7] {
  font-size: 0.875rem;
  color: #1a1a1a;
  font-weight: 500;
}
.process-action[data-v-d6dc95a7] {
  flex-shrink: 0;
}
.receive-btn[data-v-d6dc95a7] {
  background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 0.0625rem 0.1875rem rgba(0, 122, 255, 0.3);
  min-width: 2.5rem;
}
.receive-btn[data-v-d6dc95a7]:active {
  transform: translateY(0.03125rem);
  box-shadow: 0 0.03125rem 0.125rem rgba(0, 122, 255, 0.3);
}
.receive-btn.disabled[data-v-d6dc95a7] {
  background: #d1d5db;
  color: #9ca3af;
  box-shadow: none;
  transform: none;
}
.completed-text[data-v-d6dc95a7] {
  font-size: 0.75rem;
  color: #ffa726;
  background-color: #fff3e0;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  min-width: 2.5rem;
  text-align: center;
}

/* 操作选项卡片 */
.option-card[data-v-d6dc95a7] {
  padding: 1rem;
}
.checkbox-item[data-v-d6dc95a7] {
  margin-bottom: 0.75rem;
}
.custom-checkbox[data-v-d6dc95a7] {
  font-size: 0.875rem;
  color: #666666;
}
.cropping-link[data-v-d6dc95a7] {
  text-align: center;
}
.link-text[data-v-d6dc95a7] {
  font-size: 1rem;
  color: #007aff;
  font-weight: 500;
  background-color: #f0f8ff;
  padding: 0.625rem 1.25rem;
  border-radius: 0.375rem;
  display: inline-block;
}

/* 底部按钮区 */
.bottom-actions[data-v-d6dc95a7] {
  position: fixed;
  left: 0.625rem;
  right: 0.625rem;
  bottom: calc(constant(safe-area-inset-bottom) + 0.625rem);
  bottom: calc(env(safe-area-inset-bottom) + 0.625rem);
  display: flex;
  gap: 0.625rem;
  background-color: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(0.3125rem);
          backdrop-filter: blur(0.3125rem);
  padding: 0.625rem;
  border-radius: 0.5rem;
  box-shadow: 0 -0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.primary-btn[data-v-d6dc95a7] {
  flex: 2;
  background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  height: 2.75rem;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 0.1875rem 0.625rem rgba(0, 122, 255, 0.4);
}
.primary-btn[data-v-d6dc95a7]:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.125rem 0.5rem rgba(0, 122, 255, 0.4);
}
.disabled-btn[data-v-d6dc95a7] {
  flex: 2;
  background-color: #e9ecef;
  color: #adb5bd;
  border: none;
  border-radius: 0.5rem;
  height: 2.75rem;
  font-size: 1rem;
  font-weight: 600;
}
.secondary-btn[data-v-d6dc95a7] {
  flex: 1;
  background-color: #f8f9fa;
  color: #495057;
  border: 0.0625rem solid #dee2e6;
  border-radius: 0.5rem;
  height: 2.75rem;
  font-size: 1rem;
  font-weight: 500;
}
.secondary-btn[data-v-d6dc95a7]:active {
  background-color: #e9ecef;
}

/* 下滑指示器样式 */
.swipe-indicator[data-v-d6dc95a7] {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 0 0.25rem;
  background-color: white;
}
.swipe-bar[data-v-d6dc95a7] {
  width: 2.5rem;
  height: 0.25rem;
  background-color: #d1d5db;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

/* 打印弹窗样式 */
.print-modal[data-v-d6dc95a7] {
  background-color: #ffffff;
  border-radius: 0.75rem 0.75rem 0 0;
  overflow: hidden;
  max-height: 80vh;
}
.modal-header[data-v-d6dc95a7] {
  padding: 1.25rem 1rem 0.75rem 1rem;
  border-bottom: 0.03125rem solid #f0f2f5;
  text-align: center;
}
.modal-title[data-v-d6dc95a7] {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
}
.modal-content[data-v-d6dc95a7] {
  padding: 1rem;
  max-height: 60vh;
  overflow-y: auto;
}
.print-info[data-v-d6dc95a7] {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.print-row[data-v-d6dc95a7] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.625rem 0;
  border-bottom: 0.03125rem solid #f5f5f5;
}
.print-label[data-v-d6dc95a7] {
  font-size: 0.875rem;
  color: #666666;
  width: 3.75rem;
  flex-shrink: 0;
}
.print-value[data-v-d6dc95a7] {
  font-size: 0.875rem;
  color: #1a1a1a;
  font-weight: 500;
  flex: 1;
  text-align: right;
}
.qr-container[data-v-d6dc95a7] {
  display: flex;
  justify-content: center;
  padding: 1.25rem 0;
  border-bottom: none;
}
.qrcode[data-v-d6dc95a7] {
  width: 12.5rem;
  height: 12.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.modal-actions[data-v-d6dc95a7] {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
}
.cancel-print-btn[data-v-d6dc95a7] {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  padding: 0.625rem 1.875rem;
  font-size: 0.9375rem;
  font-weight: 600;
  box-shadow: 0 0.125rem 0.5rem rgba(255, 107, 107, 0.3);
}
.cancel-print-btn[data-v-d6dc95a7]:active {
  transform: translateY(0.0625rem);
  box-shadow: 0 0.0625rem 0.375rem rgba(255, 107, 107, 0.3);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
.page-container[data-v-d6dc95a7] {
    padding: 0.5rem;
}
.card-header[data-v-d6dc95a7], .info-list[data-v-d6dc95a7], .process-list[data-v-d6dc95a7] {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}
.bottom-actions[data-v-d6dc95a7] {
    left: 0.5rem;
    right: 0.5rem;
    padding: 0.5rem;
}
}
/* 兼容之前的样式，避免冲突 */
.printer[data-v-d6dc95a7], .search[data-v-d6dc95a7], .search-single[data-v-d6dc95a7], .btnList[data-v-d6dc95a7],
.printerSet[data-v-d6dc95a7], .printerSetTitle[data-v-d6dc95a7], .printerSetContent[data-v-d6dc95a7],
.colorLink[data-v-d6dc95a7], .queryCropping[data-v-d6dc95a7], .receveFlag[data-v-d6dc95a7], .colorLinkLarge[data-v-d6dc95a7],
.txtContent[data-v-d6dc95a7], .table[data-v-d6dc95a7], .scrollView[data-v-d6dc95a7], .scrollShowAll[data-v-d6dc95a7],
.total[data-v-d6dc95a7], .contentCount[data-v-d6dc95a7], .detail[data-v-d6dc95a7], .title[data-v-d6dc95a7], .titleView[data-v-d6dc95a7],
.totleDetial[data-v-d6dc95a7], .txtNotion[data-v-d6dc95a7], .btnGroup[data-v-d6dc95a7], .addLink[data-v-d6dc95a7], .addLinkCJD[data-v-d6dc95a7],
.group[data-v-d6dc95a7], .task[data-v-d6dc95a7], .child_with[data-v-d6dc95a7], .txtNomal[data-v-d6dc95a7], .txtDoing[data-v-d6dc95a7],
.txtAudit[data-v-d6dc95a7], .txtFinish[data-v-d6dc95a7], .optionClass[data-v-d6dc95a7], .titleStr[data-v-d6dc95a7],
.pickerView[data-v-d6dc95a7], .uContent[data-v-d6dc95a7], .disabled[data-v-d6dc95a7], .edit[data-v-d6dc95a7], .reback[data-v-d6dc95a7],
.addBtn[data-v-d6dc95a7], .submitBottom[data-v-d6dc95a7], .printTitle[data-v-d6dc95a7], .btnsave[data-v-d6dc95a7], .jobs[data-v-d6dc95a7] {
  display: none !important;
}